<?php
/**
 * AJAX handler untuk mengambil data tahun akreditasi
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mengambil data tahun akreditasi berdasarkan provinsi
    $sql = "SELECT id_mapping_tahun, nama_tahun 
            FROM mapping_validasi_tahun_2024 
            WHERE provinsi_id = ? 
            ORDER BY nama_tahun DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $provinsi_id_session);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            'id_mapping_tahun' => $row['id_mapping_tahun'],
            'nama_tahun' => $row['nama_tahun']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
        'data' => []
    ]);
    error_log("Error in get_tahun_akreditasi.php: " . $e->getMessage());
}
?>
