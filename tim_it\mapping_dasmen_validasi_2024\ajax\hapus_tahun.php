<?php
/**
 * AJAX handler untuk menghapus tahun akreditasi
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['id_mapping_tahun']) || empty($_POST['id_mapping_tahun'])) {
        throw new Exception('ID tahun tidak valid');
    }
    
    $id_mapping_tahun = intval($_POST['id_mapping_tahun']);
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Cek apakah data ada dan milik provinsi yang sama
    $check_sql = "SELECT nama_tahun FROM mapping_validasi_tahun_2024 
                  WHERE id_mapping_tahun = ? AND provinsi_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("ii", $id_mapping_tahun, $provinsi_id_session);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows == 0) {
        throw new Exception('Data tahun tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $tahun_data = $check_result->fetch_assoc();
    $nama_tahun = $tahun_data['nama_tahun'];
    
    // Cek apakah tahun masih digunakan di mapping_validasi_2024
    $usage_sql = "SELECT COUNT(*) as count FROM mapping_validasi_2024 
                  WHERE tahun_akreditasi = ? AND provinsi_id = ?";
    $usage_stmt = $conn->prepare($usage_sql);
    $usage_stmt->bind_param("si", $nama_tahun, $provinsi_id_session);
    $usage_stmt->execute();
    $usage_result = $usage_stmt->get_result();
    $usage_count = $usage_result->fetch_assoc()['count'];
    
    if ($usage_count > 0) {
        throw new Exception('Tahun ' . $nama_tahun . ' tidak dapat dihapus karena masih digunakan dalam ' . $usage_count . ' mapping validasi');
    }
    
    // Hapus tahun
    $delete_sql = "DELETE FROM mapping_validasi_tahun_2024 
                   WHERE id_mapping_tahun = ? AND provinsi_id = ?";
    $delete_stmt = $conn->prepare($delete_sql);
    $delete_stmt->bind_param("ii", $id_mapping_tahun, $provinsi_id_session);
    
    if (!$delete_stmt->execute()) {
        throw new Exception('Gagal menghapus tahun akreditasi: ' . $delete_stmt->error);
    }
    
    // Cek apakah ada baris yang terpengaruh
    if ($delete_stmt->affected_rows == 0) {
        throw new Exception('Tidak ada data yang dihapus');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    echo json_encode([
        'success' => true,
        'message' => 'Tahun akreditasi ' . $nama_tahun . ' berhasil dihapus',
        'data' => [
            'id_mapping_tahun' => $id_mapping_tahun,
            'nama_tahun' => $nama_tahun
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    $conn->rollback();
    $conn->autocommit(true);
    
    // Error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    error_log("Error in hapus_tahun.php: " . $e->getMessage());
}
?>
