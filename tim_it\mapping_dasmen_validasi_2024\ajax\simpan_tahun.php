<?php
/**
 * AJAX handler untuk menyimpan/update tahun akreditasi
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['nama_tahun']) || empty(trim($_POST['nama_tahun']))) {
        throw new Exception('Tahun akreditasi harus diisi');
    }
    
    $nama_tahun = trim($_POST['nama_tahun']);
    $form_action = isset($_POST['form_action']) ? $_POST['form_action'] : 'add';
    $id_mapping_tahun = isset($_POST['id_mapping_tahun']) ? intval($_POST['id_mapping_tahun']) : 0;
    
    // Validasi format tahun (4 digit)
    if (!preg_match('/^\d{4}$/', $nama_tahun)) {
        throw new Exception('Tahun harus berupa 4 digit angka');
    }
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Begin transaction
    $conn->autocommit(false);
    
    if ($form_action === 'add') {
        // Cek duplikasi tahun untuk provinsi yang sama
        $check_sql = "SELECT COUNT(*) as count FROM mapping_validasi_tahun_2024 
                      WHERE nama_tahun = ? AND provinsi_id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $nama_tahun, $provinsi_id_session);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        $count = $check_result->fetch_assoc()['count'];
        
        if ($count > 0) {
            throw new Exception('Tahun ' . $nama_tahun . ' sudah ada untuk provinsi Anda');
        }
        
        // Insert tahun baru
        $insert_sql = "INSERT INTO mapping_validasi_tahun_2024 (nama_tahun, provinsi_id) VALUES (?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param("si", $nama_tahun, $provinsi_id_session);
        
        if (!$insert_stmt->execute()) {
            throw new Exception('Gagal menyimpan tahun akreditasi: ' . $insert_stmt->error);
        }
        
        $message = 'Tahun akreditasi ' . $nama_tahun . ' berhasil ditambahkan';
        
    } else if ($form_action === 'edit') {
        // Validasi ID untuk edit
        if ($id_mapping_tahun <= 0) {
            throw new Exception('ID tahun tidak valid');
        }
        
        // Cek apakah data ada dan milik provinsi yang sama
        $check_sql = "SELECT COUNT(*) as count FROM mapping_validasi_tahun_2024 
                      WHERE id_mapping_tahun = ? AND provinsi_id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("ii", $id_mapping_tahun, $provinsi_id_session);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        $count = $check_result->fetch_assoc()['count'];
        
        if ($count == 0) {
            throw new Exception('Data tahun tidak ditemukan atau Anda tidak memiliki akses');
        }
        
        // Cek duplikasi tahun untuk provinsi yang sama (kecuali data yang sedang diedit)
        $check_dup_sql = "SELECT COUNT(*) as count FROM mapping_validasi_tahun_2024 
                          WHERE nama_tahun = ? AND provinsi_id = ? AND id_mapping_tahun != ?";
        $check_dup_stmt = $conn->prepare($check_dup_sql);
        $check_dup_stmt->bind_param("sii", $nama_tahun, $provinsi_id_session, $id_mapping_tahun);
        $check_dup_stmt->execute();
        $check_dup_result = $check_dup_stmt->get_result();
        $dup_count = $check_dup_result->fetch_assoc()['count'];
        
        if ($dup_count > 0) {
            throw new Exception('Tahun ' . $nama_tahun . ' sudah ada untuk provinsi Anda');
        }
        
        // Update tahun
        $update_sql = "UPDATE mapping_validasi_tahun_2024 SET nama_tahun = ? 
                       WHERE id_mapping_tahun = ? AND provinsi_id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("sii", $nama_tahun, $id_mapping_tahun, $provinsi_id_session);
        
        if (!$update_stmt->execute()) {
            throw new Exception('Gagal mengupdate tahun akreditasi: ' . $update_stmt->error);
        }
        
        $message = 'Tahun akreditasi berhasil diupdate menjadi ' . $nama_tahun;
    } else {
        throw new Exception('Aksi tidak valid');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => [
            'nama_tahun' => $nama_tahun,
            'action' => $form_action
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    $conn->rollback();
    $conn->autocommit(true);
    
    // Error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    error_log("Error in simpan_tahun.php: " . $e->getMessage());
}
?>
