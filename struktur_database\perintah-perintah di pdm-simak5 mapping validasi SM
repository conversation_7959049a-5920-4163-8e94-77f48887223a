Berikut ini adalah struktur tabel "mapping_validasi_2024" :
id_mapping	int(11)
sekolah_id	int(11)
kd_asesor1	varchar(25)
kd_asesor2	varchar(25)
tgl_mulai_validasi	date
tgl_akhir_validasi	date
no_surat_validasi	varchar(30)
tgl_surat_validasi	date
tahap	int(11)
tahun_akreditasi	varchar(4)
file_format_5_1_berita_acara_hasil_validasi_1	varchar(50)
file_format_5_1_berita_acara_hasil_validasi_2 varchar(50)
file_pakta_integritas_1	varchar(50)
file_pakta_integritas_2	varchar(50)
file_st_validasi	varchar(50)
provinsi_id	int(11)

Berikut ini struktur tabel "sekolah" :
sekolah_id int(11)
nama_sekolah varchar(100)
npsn varchar(30)
jenjang_id int(1)
rumpun varchar(20)
alamat text
tipe_sekolah_id int(1)
status_sekolah_id int(1)
provinsi_id int(2)
kota_id int(6)
desa_kel<PERSON>han varchar(200)
kecamatan varchar(100)
nama_kepsek varchar(100)
no_hp_kepsek varchar(50)
no_wa_kepsek varchar(50)
nama_operator varchar(50)
no_hp_operator varchar(20)
no_wa_operator varchar(20)
email varchar(100)
nama_yayasan varchar(200)
no_akte varchar(50)
tahun_berdiri varchar(4)
status_keaktifan_id varchar(1)
soft_delete rchar(1)

Berikut ini adalah struktur tabel "jenjang" :
id_jenjang int(11)
jenjang_id varchar(2)
nm_jenjang varchar(15)

Berikut ini struktur tabel "kab_kota" :
id_kota	int(11)	
kota_id	varchar(10)
nm_kota	varchar(50)
provinsi_id	int(11)
kd_user	varchar(25)

Berikut ini struktur tabel "asesor_1" :
id_asesor1int(11)
kd_asesor1 varchar(25)
nia1 varchar(20) 
nm_asesor1	varchar(100) 
ktp	varchar(20) 
unit_kerja	varchar(300) 
kota_id1 varchar(10)
provinsi_id	int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan	varchar(50)
jabatan_struktural varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat	date
kegiatan varchar(50)
status_keaktifan_id	varchar(1)
sebab text
kd_user	varchar(50)
soft_delete	varchar(1)

Berikut ini tabel "asesor_2" :
id_asesor2 int(11)
kd_asesor2 varchar(25)
nia2 varchar(20)
nm_asesor2 varchar(100)
ktp varchar(20)
unit_kerja varchar(300)
kota_id2 varchar(10)
provinsi_id	int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan	varchar(50)
jabatan_struktural	varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id	varchar(1)
sebab text
kd_user	varchar(50)
soft_delete	varchar(1)

Berikut ini struktur tabel mapping_validasi_tahun_2024 :
id_mapping_tahun int(11)
nama_tahun	int(4)
provinsi_id	int(11)

==================================================================================================================

buatlah modul "Mapping Asesor Validasi SM" pada direktori tim_it/mapping_dasmen_validasi_2024/validasi.php, juga tersedia sub direktori ajax dan js, tabel header yang digunakan adalah :
NO (auto increment)
NPSN (sekolah.npsn)
NAMA SEKOLAH (sekolah.nama_sekolah)
JENJANG (jenjang.nm_jenjang, sekolah.jenjang_id=jenjang.jenjang_id)
RUMPUN (sekolah.rumpun)
KAB/KOTA (kab_kota.nm_kota, sekolah.kota_id=kab_kota.kota_id)
NIA VALIDATOR 1 (asesor_1.nia1)
NAMA VALIDATOR 1 (asesor_1.nm_asesor1)
NIA VALIDATOR 2 (asesor_2.nia2)
NAMA VALIDATOR 2 (asesor_2.nm_asesor2)
TAHUN AKREDITASI (mapping_validasi_2024.tahun_akreditasi)
TAHAP Validasi (mapping_validasi_2024.tahap)
AKSI
Untuk kolom AKSI tampilkan tombol ikon detail (tombolnya saja dulu), sampai disini apakah anda sudah mengerti, ataukah ada yang perlu ditanyakan?

oh iya lupa, tambahkan
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>

where mapping_validasi_2024.provinsi_id = provinsi_id session login

tambahkan juga tombol "Input Data Mapping" dan tombol "Export Excel", "Import Excel" dan "Tahun Akreditasi" (buatkan tombolnya saja dulu)
=============================================================================================================

sekarang anda akan meng-aktifkan fungsi tombol "Input Data Mapping", jika tombol tersebut di-klik akan tampil modal "Input Data Mapping" dengan form sebagi berikut:

- label NPSN Sekolah (form input text) akan menyimpan ke field mapping_validasi_2024.sekolah_id (required)

- label NIA Validator 1 (form input text) akan menyimpan ke field mapping_validasi_2024.kd_asesor1 (required)

- label Nia Validator 2 (form input text) akan menyimpan ke field mapping_validasi_2024.kd_asesor2 (required)

- label Tanggal Mulai Validasi (form input date) akan menyimpan ke field mapping_validasi_2024.tgl_mulai_Validasi

- label Tanggal Akhir Validasi (form input date) akan menyimpan ke field mapping_validasi_2024.tgl_akhir_Validasi

- label Tanggal Surat Tugas Validasi (form input date) akan menyimpan ke field mapping_validasi_2024.tgl_surat_validasi

- label Nomor Surat Tugas Validasi (form input date) akan menyimpan ke field mapping_validasi_2024.no_surat_tugas_Validasi

- label Tahun Akreditasi (form input text) akan menyimpan ke field mapping_validasi_2024.tahun_akreditasi (required)

- label Tahap Ke (form input text) akan menyimpan ke field mapping_validasi_2024.tahap (required)

perlu lookup untuk input NPSN menjadi sekolah_id, NIA menjadi kd_asesor1, kd_asesor2,

proses simpan modal "Input Data Mapping" ini terjadi tanpa refresh browser (tanpa reload halaman) sehingga tampilan data di tabel utama pada modul "Mapping Asesor Validasi SM" terjadi secara real time tanpa refresh browser (tanpa reload halaman)

sampai disini apakah ada yang ingin anda tanyakan?
=============================================================================================

Sekarang kita akan membuat modal "Detail Mapping Validasi SM".
Ketika tombol icon detail yang ada di kolom "Aksi" pada tabel utama modul "Mapping Asesor Validasi SM" di-klik maka akan muncul sebuah modal dengan judul modal adalah "Detail Mapping Validasi SM" dengan ukuran modal-xl.
Di dalam modal "Detail Mapping Validasi SM" terdapat lima buah kolom, masing-masing kolom memiliki judul kolom dan isi kolom sebagai berikut:
Kolom pertama dengan judul kolom "Data Sekolah" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label NPSN dengan kolom disebelahnya berisi field sekolah.npsn
- Label Nama Sekolah  dengan kolom disebelahnya berisi field sekolah.nama_sekolah
- Label Jenjang dengan kolom disebelahnya berisi field jenjang.nm_jenjang
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota
- Label Nama Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.nama_kepsek
- Label HP Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.no_hp_kepsek
- No WA Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.no_wa_kepsek

Kolom kedua dengan judul kolom "Data Asesor" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label Asesor 1 (colspan=2)
- Label NIA dengan kolom disebelahnya berisi field asesor_1.nia1
- Label Nama dengan kolom disebelahnya berisi field asesor_1.nm_asesor1
- Label No HP dengan kolom disebelahnya berisi field asesor_1.no_hp
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota
- Label Asesor 2 (colspan=2)
- Label NIA dengan kolom disebelahnya berisi field asesor_2.nia2
- Label Nama dengan kolom disebelahnya berisi field asesor_2.nm_asesor2
- Label No HP dengan kolom disebelahnya berisi field asesor_2.no_hp
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota

Kolom ketiga dengan judul kolom "Dokumen Unggahan" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:

- Label File Format 3.1 Hasil Penilaian Pra-Validasi 1 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_3_1_hasil_penilaian_pra_Validasi_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 3.1 Hasil Penilaian Pra-Validasi 2 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_3_1_hasil_penilaian_pra_Validasi_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 3.2 LK Penggalian Data Pra-Validasi 1 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_3_2_lk_penggalian_data_pra_Validasi_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 3.2 LK Penggalian Data Pra-Validasi 2 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_3_2_lk_penggalian_data_pra_Validasi_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 4.1 Surat Tugas Validasi dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_4_1_surat_tugas_Validasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 4.2 Pakta Integritas 1 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_4_2_pakta_integritas_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 4.2 Pakta Integritas 2 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_4_2_pakta_integritas_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 4.3 Rekap Penggalian Data Penilaian 1 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_4_3_lembar_rekap_penggalian_data_penilaian_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 4.3 Rekap Penggalian Data Penilaian 2 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_4_3_lembar_rekap_penggalian_data_penilaian_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 4.4 Berita Acara Validasi dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_4_4_berita_acara_Validasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 4.5 Laporan Individu 1 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_4_5_laporan_individu_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 4.5 Laporan Individu 2 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_4_5_laporan_individu_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 4.5 Laporan Kelompok dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_4_5_laporan_kelompok ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Format 4.5 Catatan Dan Saran dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_4_5_catatan_dan_saran ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Foto Validasi dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_foto_Validasi_2024 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah


Kolom keempat dengan judul kolom "Pelaksanaan Kegiatan" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label Tanggal Pra-Validasi dengan kolom disebelahnya berisi field mapping_validasi_2024.tgl_pra_Validasi
- Label No. ST Pra-Validasi dengan kolom disebelahnya berisi field mapping_validasi_2024.no_surat_tugas_pra_Validasi
- Label Tanggal ST Pra-Validasi dengan kolom disebelahnya berisi mapping_validasi_2024.field tgl_surat_tugas_pra_Validasi
- Label Tanggal Validasi Dimulai dengan kolom disebelahnya berisi mapping_validasi_2024.field tgl_mulai_Validasi
- Label Tanggal Validasi Berakhir dengan kolom disebelahnya berisi field mapping_validasi_2024.tgl_akhir_Validasi
- Label No. ST Validasi dengan kolom disebelahnya berisi field mapping_validasi_2024.no_surat_tugas_Validasi
- Label Tanggal Surat Tugas Validasi dengan kolom disebelahnya berisi field mapping_validasi_2024.tgl_surat_tugas_Validasi
- Tahap dengan kolom di sebelahnya berisi field mapping_validasi_2024.tahap

Kolom kelima dengan judul kolom "Aksi" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Tombol "Edit Asesor Perubahan"
- Tombol "Edit Tanggal Kegiatan"
- Tombol "Hapus"
- Tombol "Download Surat Tugas Validasi"
- Tombol "Download Surat Tugas Pra-Validasi"
================================================================================================================

baik kawan, kita akan melanjutkan modul "Mapping Asesor Validasi SM" ini, pada kolom "Dokumen Unggahan" yang ada di modal "Detail Mapping Validasi SM" tertulis "Sudah Upload" dan "Belum Upload", jika yang tampil adalah tulisan "Sudah Upload" maka tulisan tersebut bisa di-klik sedangkan tulisan "Belum Upload" tidak bisa di-klik, untuk tulisan "Sudah Upload" jika di-klik akan membuka tab baru yang menampilkan file PDF. OK kita jelaskan lebih rinci lagi sebagai berikut:

- File Format 3.1 Hasil Penilaian Pra-Validasi 1 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_3_1_hasil_penilaian_pra_Validasi_1/, untuk nama file diambil dari field mapping_validasi_2024.file_format_3_1_hasil_penilaian_pra_Validasi_1

- File Format 3.1 Hasil Penilaian Pra-Validasi 2 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_3_1_hasil_penilaian_pra_Validasi_2/, untuk nama file diambil dari field mapping_validasi_2024.file_format_3_1_hasil_penilaian_pra_Validasi_2

- File Format 3.2 LK Penggalian Data Pra-Validasi 1 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_3_2_lk_penggalian_data_pra_Validasi_1/, untuk nama file diambil dari field mapping_validasi_2024.file_format_3_2_lk_penggalian_data_pra_Validasi_1

- File Format 3.2 LK Penggalian Data Pra-Validasi 2 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_3_2_lk_penggalian_data_pra_Validasi_2/, untuk nama file diambil dari field mapping_validasi_2024.file_format_3_2_lk_penggalian_data_pra_Validasi_2

- File Format 4.1 Surat Tugas Validasi jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_4_1_surat_tugas_Validasi/, untuk nama file diambil dari field mapping_validasi_2024.file_format_4_1_surat_tugas_Validasi

- File Format 4.2 Pakta Integritas 1 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_4_2_pakta_integritas_1/, untuk nama file diambil dari field mapping_validasi_2024.file_format_4_2_pakta_integritas_1

- File Format 4.2 Pakta Integritas 2 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_4_2_pakta_integritas_2/, untuk nama file diambil dari field mapping_validasi_2024.file_format_4_2_pakta_integritas_2

- File Format 4.3 Rekap Penggalian Data Penilaian 1 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_4_3_lembar_rekap_penggalian_data_penilaian_1/, untuk nama file diambil dari field mapping_validasi_2024.file_format_4_3_lembar_rekap_penggalian_data_penilaian_1

- File Format 4.3 Rekap Penggalian Data Penilaian 2 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_4_3_lembar_rekap_penggalian_data_penilaian_2/, untuk nama file diambil dari field mapping_validasi_2024.file_format_4_3_lembar_rekap_penggalian_data_penilaian_2

- File Format 4.4 Berita Acara Validasi jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_4_4_berita_acara_Validasi/, untuk nama file diambil dari field mapping_validasi_2024.file_format_4_4_berita_acara_Validasi

- File Format 4.5 Laporan Individu 1 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_4_5_laporan_individu_1/, untuk nama file diambil dari field mapping_validasi_2024.file_format_4_5_laporan_individu_1

- File Format 4.5 Laporan Individu 2 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_4_5_laporan_individu_2/, untuk nama file diambil dari field mapping_validasi_2024.file_format_4_5_laporan_individu_2

- File Format 4.5 Laporan Kelompok jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_4_5_laporan_kelompok/, untuk nama file diambil dari field mapping_validasi_2024.file_format_4_5_laporan_kelompok

- File Format 4.5 Catatan Dan Saran jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_format_4_5_catatan_dan_saran/, untuk nama file diambil dari field mapping_validasi_2024.file_format_4_5_catatan_dan_saran

- File Foto Validasi jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_foto_Validasi_2024/, untuk nama file diambil dari field mapping_validasi_2024.file_foto_Validasi_2024

Sampai disini apakah anda mengerti? silahkan bertanya jika belum paham
================================================================================================================


baik kawan, kita akan melanjutkan modul "Mapping Asesor Validasi SM" ini, pada kolom "AKSI" yang ada di modal "Detail Mapping Validasi SM" terdapat tombol "Edit Tanggal" jika tombol tersebut di-klik akan menampilkan sebuah modal dengan ukuran kecil yang berisi form untuk edit atau update field mapping_validasi_2024.tgl_mulai_Validasi dan field mapping_validasi_2024.tgl_akhir_Validasi, perlu saya sampaikan bahwa dengan tampilnya modal yang berisi form edit tersebut tidak serta merta menutup modal "Detail Mapping Validasi SM", nantinya modal untuk edit/update mapping_validasi_2024.tgl_mulai_Validasi dan field mapping_validasi_2024.tgl_akhir_Validasi berada di atas modal "Detail Mapping Validasi SM" kemudian ketika di-klik tombol "Update perubaan" maka secara otomatis "Tanggal Validasi Dimulai" dan "Tanggal Validasi Berakhir" yang ada di kolom "PELAKSANAAN KEGIATAN" berubah tanpa refresh browser, sampai disini apakah anda mengerti dengan apa yang saya maksud? jika belum mengerti silahkan bertanya

================================================================================================================

kita lanjut ke "Edit Asesor" dimana bisnis prosesnya sangat mirip dengan "Edit Tanggal" yaitu jika tombol "Edit Asesor" di-klik akan menampilkan sebuah modal dengan ukuran kecil yang berisi form untuk edit atau update field mapping_validasi_2024.kd_asesor1 dan field mapping_validasi_2024.kd_asesor2, perlu saya sampaikan bahwa dengan tampilnya modal yang berisi form edit tersebut tidak serta merta menutup modal "Detail Mapping Validasi SM", nantinya modal untuk edit/update field mapping_validasi_2024.kd_asesor1 dan field mapping_validasi_2024.kd_asesor2 berada di atas modal "Detail Mapping Validasi SM" kemudian ketika di-klik tombol "Update perubaan" maka secara otomatis "NIA", "Nama", "No. HP", "Kota" kedua asesor yang ada di kolom "DATA ASESOR" berubah tanpa refresh browser, sampai disini apakah anda mengerti dengan apa yang saya maksud? jika belum mengerti silahkan bertanya, Oh.. hampir lupa bahwa data yang diinput ke form "Edit Asesor" adalah NIA salah satu dan atau kedua asesor

================================================================================================================

di direktori "tim_it/mapping_dasmen_2024/" terdapat sebuah file dengan nama "mapping_2024_st_Validasi.php", tugas anda adalah modifikasi file tersebut untuk disesuaikan atau disinkronkan dengan pola coding yang mirip dengan "tim_it/mapping_dasmen_2020/mapping_st_Validasi.php"

baik kawan, kita akan melanjutkan modul "Mapping Asesor Validasi SM" ini, pada kolom "AKSI" yang ada di modal "Detail Mapping Validasi SM" terdapat tombol "ST Validasi" jika tombol tersebut di-klik akan membuka file mapping_2024_st_Validasi.php yang ada di root direktori "mapping_dasmen_2024"

================================================================================================================

di direktori "tim_it/mapping_dasmen_2024/" terdapat sebuah file dengan nama "mapping_2024_st_pra_akreditasi.php", tugas anda adalah modifikasi file tersebut untuk disesuaikan atau disinkronkan dengan pola coding yang mirip dengan "tim_it/mapping_dasmen_2020/mapping_st_Validasi.php"

baik kawan, kita akan melanjutkan modul "Mapping Asesor Validasi SM" ini, pada kolom "AKSI" yang ada di modal "Detail Mapping Validasi SM" terdapat tombol "ST Pra-Validasi" jika tombol tersebut di-klik akan membuka file mapping_2024_st_pra_akreditasi.php yang ada di root direktori "mapping_dasmen_2024". parameter yang digunakan adalah "mapping_2024_st_pra_akreditasi.php?kode"

================================================================================================================

baik kawan, kita akan melanjutkan modul "Mapping Asesor Validasi SM" ini, pada kolom "AKSI" yang ada di modal "Detail Mapping Validasi SM" terdapat tombol "Hapus", jika tombol tersebut di-klik, akan menghapus record data secara permanen

================================================================================================================

baiklah sobatku, sekarang kita akan memfungsikan/mengaktifkan tombol "Export Excel" di modul "Mapping Asesor Validasi SM ", anda gunakan library export excel sama seperti modul "Mapping Asesor Validasi PAUD" yang ada di direktori "tim_it\mapping_paud_Validasi_2020", data yang perlu di export di adalah:

No (autoincrement)
NPSN (sekolah.npsn)
NAMA SEKOLAH (sekolah.nama_sekolah)
JENJANG (jenjang.nm_jenjang)
RUMPUN (sekolah.rumpun)
KABUPATEN / KOTA (kab_kota.nm_kota domisili sekolah)
NIA ASESOR A (asesor_1.nia1)
NAMA ASESOR A (asesor_1.nm_asesor1)
KABUPATEN KOTA (kab_kota.nm_kota domisili asesor)
NIA ASESOR B (asesor_2.nia2)
NAMA ASESOR B (asesor_2.nm_asesor2)
KABUPATEN KOTA (kab_kota.nm_kota domisili asesor)
TAHUN AKREDITASI (mapping_validasi_2024.tahun_akreditasi)
TAHAP Validasi (mapping_validasi_2024.tahap)
TANGGAL Validasi (mapping_validasi_2024.tgl_mulai_Validasi)
NOMOR SURAT TUGAS (mapping_validasi_2024.no_surat_tugas_Validasi)
TANGGAL SURAT TUGAS (mapping_validasi_2024.tgl_surat_tugas_Validasi)

FILE FORMAT 3.1 HASIL PENILAIAN PRA-Validasi 1 (jika mapping_validasi_2024.file_format_3_1_hasil_penilaian_pra_Validasi_1 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 3.1 HASIL PENILAIAN PRA-Validasi 2 (jika mapping_validasi_2024.file_format_3_1_hasil_penilaian_pra_Validasi_2 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 3.2 LEMBAR KERJA PENGGALIAN DATA PRA-Validasi 1 (jika mapping_validasi_2024.file_format_3_2_lk_penggalian_data_pra_Validasi_1 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 3.2 LEMBAR KERJA PENGGALIAN DATA PRA-Validasi 2 (jika mapping_validasi_2024.file_format_3_2_lk_penggalian_data_pra_Validasi_2 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 4.1 SURAT TUGAS Validasi (jika mapping_validasi_2024.file_format_4_1_surat_tugas_Validasi terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 4.2 PAKTA INTEGRITAS 1 (jika mapping_validasi_2024.file_format_4_2_pakta_integritas_1 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 4.2 PAKTA INTEGRITAS 2 (jika mapping_validasi_2024.file_format_4_2_pakta_integritas_2 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 4.3 LEMBAR REKAP PENGGALIAN DATA PENILAIAN 1 (jika mapping_validasi_2024.file_format_4_3_lembar_rekap_penggalian_data_penilaian_1 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 4.3 LEMBAR REKAP PENGGALIAN DATA PENILAIAN 2 (jika mapping_validasi_2024.file_format_4_3_lembar_rekap_penggalian_data_penilaian_2 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 4.4 BERITA ACARA Validasi (jika mapping_validasi_2024.file_format_4_4_berita_acara_Validasi terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 4.5 LAPORAN INDIVIDU 1 (jika mapping_validasi_2024.file_format_4_5_laporan_individu_1 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 4.5 LAPORAN INDIVIDU 2 (jika mapping_validasi_2024.file_format_4_5_laporan_individu_2 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 4.5 LAPORAN KELOMPOK (jika mapping_validasi_2024.file_format_4_5_laporan_kelompok terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FORMAT 4.5 CATATAN DAN SARAN (jika mapping_validasi_2024.file_format_4_5_catatan_dan_saran terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FOTO Validasi (jika mapping_validasi_2024.file_foto_Validasi_2024 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

data yang di download disesuaikan dengan filter where mapping_validasi_tahun_2024.nama_tahun

jika anda belum paham dengan maksud saya silahkan bertanya

Oh iya ini saya ada contoh query yang bisa anda gunakan:
SELECT mapping_validasi_2024.*, sekolah.npsn, sekolah.nama_sekolah, sekolah.rumpun, jenjang.nm_jenjang, mapping_validasi_2024.tahun_akreditasi, mapping_validasi_tahun_2024.nama_tahun, kab_kota.nm_kota, asesor_1.nia1, asesor_1.nm_asesor1, asesor_1.no_hp as hp1,
                (SELECT kab_kota.nm_kota from asesor_1 LEFT JOIN kab_kota ON asesor_1.kota_id1=kab_kota.kota_id
                 WHERE mapping_validasi_2024.kd_asesor1=asesor_1.kd_asesor1) as kota1, 
                asesor_2.nia2, asesor_2.nm_asesor2, asesor_2.no_hp as hp2,
                (SELECT kab_kota.nm_kota from asesor_2 LEFT JOIN kab_kota ON asesor_2.kota_id2=kab_kota.kota_id
                 WHERE mapping_validasi_2024.kd_asesor2=asesor_2.kd_asesor2) as kota2 FROM mapping_2024
                LEFT JOIN sekolah ON mapping_validasi_2024.sekolah_id=sekolah.sekolah_id
                LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
                LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
                LEFT JOIN asesor_1 ON mapping_validasi_2024.kd_asesor1=asesor_1.kd_asesor1
                LEFT JOIN asesor_2 ON mapping_validasi_2024.kd_asesor2=asesor_2.kd_asesor2
                LEFT JOIN mapping_validasi_tahun_2024 ON mapping_validasi_2024.tahun_akreditasi=mapping_validasi_tahun_2024.nama_tahun
                AND sekolah.rumpun ='dasmen'
                WHERE mapping_validasi_2024.tahun_akreditasi = mapping_validasi_tahun_2024.nama_tahun
                AND mapping_validasi_tahun_2024.provinsi_id = '$provinsi_id' (session login)
                AND mapping_validasi_2024.provinsi_id = '$provinsi_id' (session login)

=================================================================================================================