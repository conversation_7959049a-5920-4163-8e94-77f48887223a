<?php
/**
 * File checker untuk validasi keberadaan file sebelum dibuka
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

try {
    // Validasi parameter
    if (!isset($_GET['file']) || !isset($_GET['type'])) {
        throw new Exception('Parameter tidak lengkap');
    }
    
    $filename = $_GET['file'];
    $fileType = $_GET['type'];
    
    // Tentukan path berdasarkan jenis file
    $basePath = '../../../simak/files/';
    switch($fileType) {
        case 'ba1':
            $path = $basePath . 'upload_file_format_5_1_berita_acara_hasil_validasi_1/';
            break;
        case 'ba2':
            $path = $basePath . 'upload_file_format_5_1_berita_acara_hasil_validasi_2/';
            break;
        case 'pakta1':
            $path = $basePath . 'upload_file_pakta_integritas_validasi_1/';
            break;
        case 'pakta2':
            $path = $basePath . 'upload_file_pakta_integritas_validasi_2/';
            break;
        case 'st':
            $path = $basePath . 'upload_file_st_validasi/';
            break;
        default:
            throw new Exception('Jenis file tidak valid');
    }
    
    $fullPath = $path . $filename;
    
    // Cek apakah file ada
    if (file_exists($fullPath)) {
        // Redirect ke file
        header('Location: ' . $fullPath);
        exit;
    } else {
        // File tidak ditemukan
        echo '<!DOCTYPE html>
        <html>
        <head>
            <title>File Tidak Ditemukan</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
        </head>
        <body class="bg-light">
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white text-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-exclamation-triangle"></i> File Tidak Ditemukan
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <i class="fas fa-file-times fa-4x text-danger mb-3"></i>
                                <h6 class="text-danger">File yang Anda cari tidak ditemukan di server</h6>
                                <p class="text-muted mb-4">
                                    <strong>File:</strong> ' . htmlspecialchars($filename) . '<br>
                                    <strong>Path:</strong> ' . htmlspecialchars($path) . '
                                </p>
                                <button type="button" class="btn btn-secondary" onclick="window.close()">
                                    <i class="fas fa-times"></i> Tutup
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>';
    }
    
} catch (Exception $e) {
    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>Error</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white text-center">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-circle"></i> Terjadi Kesalahan
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <i class="fas fa-bug fa-4x text-danger mb-3"></i>
                            <h6 class="text-danger">Terjadi kesalahan saat mengakses file</h6>
                            <p class="text-muted mb-4">' . htmlspecialchars($e->getMessage()) . '</p>
                            <button type="button" class="btn btn-secondary" onclick="window.close()">
                                <i class="fas fa-times"></i> Tutup
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>';
}
?>
