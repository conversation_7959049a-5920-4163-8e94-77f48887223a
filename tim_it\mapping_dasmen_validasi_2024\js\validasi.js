/**
 * JavaScript untuk Mapping Asesor Validasi SM
 */

$(document).ready(function() {
    // Inisialisasi DataTable
    initDataTable();

    // Event handlers
    initEventHandlers();

    // Load dropdown filter tahun
    loadDropdownFilterTahun();
});

/**
 * Inisialisasi DataTable
 */
function initDataTable() {
    $('#table-validasi').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_validasi.php',
            type: 'POST',
            data: function(d) {
                // Tambahkan filter tahun jika ada
                d.filter_tahun = $('#filter-tahun').val() || '';
            },
            error: function(xhr, error, thrown) {
                console.error('DataTables error:', error);
                showAlert('error', 'Terjadi kesalahan saat memuat data');
            }
        },
        columns: [
            { data: 0, name: 'no', orderable: false, searchable: false, className: 'text-center' },
            { data: 1, name: 'npsn', className: 'text-center' },
            { data: 2, name: 'nama_sekolah' },
            { data: 3, name: 'jenjang', className: 'text-center' },
            { data: 4, name: 'rumpun', className: 'text-center' },
            { data: 5, name: 'kab_kota' },
            { data: 6, name: 'nia1', className: 'text-center' },
            { data: 7, name: 'nama_asesor1' },
            { data: 8, name: 'nia2', className: 'text-center' },
            { data: 9, name: 'nama_asesor2' },
            { data: 10, name: 'tahun_akreditasi', className: 'text-center' },
            { data: 11, name: 'tahap', className: 'text-center', orderable: false },
            { data: 12, name: 'aksi', orderable: false, searchable: false, className: 'text-center' }
        ],
        order: [[1, 'asc']], // Order by NPSN
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            infoPostFix: "",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        responsive: true,
        autoWidth: false,
        scrollX: true,
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        drawCallback: function(settings) {
            // Inisialisasi tooltip setelah tabel digambar
            $('[data-toggle="tooltip"]').tooltip();
        }
    });
}

/**
 * Inisialisasi Event Handlers
 */
function initEventHandlers() {
    // Tombol Input Data Mapping
    $('#btn-add').on('click', function() {
        resetForm();
        $('#modal-tambah').modal('show');
    });

    // Tombol Export Excel
    $('#btn-export').on('click', function() {
        showAlert('info', 'Fitur export Excel akan ditambahkan pada tahap selanjutnya');
        // exportToExcel();
    });

    // Tombol Import Excel
    $('#btn-import').on('click', function() {
        showAlert('info', 'Fitur import Excel akan ditambahkan pada tahap selanjutnya');
        // $('#modal-import').modal('show');
    });

    // Tombol Tahun Akreditasi
    $('#btn-tahun-akreditasi').on('click', function() {
        openModalTahunAkreditasi();
    });

    // Event handler untuk tombol simpan tahun
    $('#btn-simpan-tahun').on('click', function() {
        var tahun = $('#tahun-akreditasi').val();

        if (!tahun || tahun < 2020 || tahun > 2030) {
            showAlert('warning', 'Tahun harus antara 2020-2030');
            return;
        }

        // Simpan tahun akreditasi
        $.ajax({
            url: 'ajax/get_tahun_akreditasi.php',
            type: 'POST',
            data: {
                action: 'update',
                tahun_akreditasi: tahun
            },
            dataType: 'json',
            beforeSend: function() {
                $('#btn-simpan-tahun').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
            },
            success: function(response) {
                if (response.success) {
                    $('#modal-tahun-akreditasi').modal('hide');

                    // Reload dropdown filter untuk menampilkan tahun yang baru/diupdate
                    loadDropdownFilterTahun();

                    // Set filter tahun ke tahun yang baru diupdate
                    if (!$('#filter-tahun').length) {
                        $('body').append('<input type="hidden" id="filter-tahun" value="">');
                    }
                    $('#filter-tahun').val(response.tahun);

                    // Update dropdown text
                    $('#filter-tahun-text').text(response.tahun);

                    // Tampilkan status filter
                    showFilterStatus('Tahun Akreditasi: ' + response.tahun);

                    // Refresh DataTable dengan filter tahun yang baru
                    $('#table-validasi').DataTable().ajax.reload(null, false);

                    showAlert('success', response.message + '. Tabel difilter untuk tahun ' + response.tahun);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function() {
                showAlert('error', 'Terjadi kesalahan saat menyimpan tahun');
            },
            complete: function() {
                $('#btn-simpan-tahun').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan');
            }
        });
    });

    // Form submit handler
    $('#form-tambah').on('submit', function(e) {
        e.preventDefault();
        submitForm();
    });

    // Autocomplete untuk NPSN
    initNPSNAutocomplete();

    // Autocomplete untuk NIA Validator 1 dan 2
    initNIAAutocomplete();

    // Tombol clear filter
    $('#btn-clear-filter').on('click', function() {
        clearFilter();
    });

    // Filter Tahun Akreditasi dropdown
    $(document).on('click', '#dropdown-filter-tahun a[data-tahun]', function(e) {
        e.preventDefault();
        var tahun = $(this).data('tahun');
        var tahunText = tahun ? tahun : 'Semua Tahun';

        // Update button text
        $('#filter-tahun-text').text(tahunText);

        // Set hidden input untuk filter
        if (!$('#filter-tahun').length) {
            $('body').append('<input type="hidden" id="filter-tahun" value="">');
        }
        $('#filter-tahun').val(tahun);

        // Show/hide filter status
        if (tahun) {
            showFilterStatus('Tahun Akreditasi: ' + tahun);
        } else {
            $('#filter-status').hide();
        }

        // Reload DataTable dengan filter baru
        $('#table-validasi').DataTable().ajax.reload(null, false);

        showAlert('success', 'Filter tahun akreditasi: ' + tahunText);
    });

    // Refresh DataTable ketika modal ditutup
    $('#modal-tambah, #modal-import, #modal-tahun-akreditasi').on('hidden.bs.modal', function() {
        $('#table-validasi').DataTable().ajax.reload(null, false);
    });
}

/**
 * Fungsi untuk menampilkan detail validasi
 */
function detailValidasi(idMapping) {
    showAlert('info', 'Fitur detail validasi akan ditambahkan pada tahap selanjutnya. ID: ' + idMapping);
    
    // Placeholder untuk fungsi detail
    // Nanti akan menampilkan modal dengan detail lengkap mapping validasi
    console.log('Detail validasi ID:', idMapping);
}

/**
 * Fungsi untuk menampilkan alert
 */
function showAlert(type, message) {
    var alertClass = '';
    var iconClass = '';
    
    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            iconClass = 'fas fa-check-circle';
            break;
        case 'error':
            alertClass = 'alert-danger';
            iconClass = 'fas fa-exclamation-circle';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            iconClass = 'fas fa-exclamation-triangle';
            break;
        case 'info':
        default:
            alertClass = 'alert-info';
            iconClass = 'fas fa-info-circle';
            break;
    }
    
    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    '<i class="' + iconClass + '"></i> ' + message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';
    
    $('#alert-container').html(alertHtml);
    
    // Auto hide after 5 seconds
    setTimeout(function() {
        $('#alert-container .alert').fadeOut('slow', function() {
            $(this).remove();
        });
    }, 5000);
}

/**
 * Fungsi untuk reload DataTable
 */
function reloadDataTable() {
    if ($.fn.DataTable.isDataTable('#table-validasi')) {
        $('#table-validasi').DataTable().ajax.reload(null, false);
    } else {
        initDataTable();
    }
}

/**
 * Fungsi placeholder untuk export Excel
 */
function exportToExcel() {
    // Placeholder - akan diimplementasikan nanti
    showAlert('info', 'Fungsi export Excel sedang dalam pengembangan');
}

/**
 * Fungsi placeholder untuk import Excel
 */
function importFromExcel() {
    // Placeholder - akan diimplementasikan nanti
    showAlert('info', 'Fungsi import Excel sedang dalam pengembangan');
}

/**
 * Reset form input data mapping
 */
function resetForm() {
    $('#form-tambah')[0].reset();
    $('#sekolah_id').val('');
    $('#kd_asesor1').val('');
    $('#kd_asesor2').val('');
    $('#sekolah-info').text('');
    $('#asesor1-info').text('');
    $('#asesor2-info').text('');
    $('#modal-alert-container').html('');
}

/**
 * Inisialisasi autocomplete untuk NPSN
 */
function initNPSNAutocomplete() {
    let npsnTimeout;

    $('#npsn').on('input', function() {
        const search = $(this).val().trim();

        // Clear previous timeout
        clearTimeout(npsnTimeout);

        // Clear previous data
        $('#sekolah_id').val('');
        $('#sekolah-info').text('');

        if (search.length < 2) {
            return;
        }

        // Set timeout untuk menghindari terlalu banyak request
        npsnTimeout = setTimeout(function() {
            $.ajax({
                url: 'ajax/autocomplete_npsn.php',
                type: 'POST',
                data: { search: search },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        showNPSNSuggestions(response.data);
                    } else {
                        hideNPSNSuggestions();
                    }
                },
                error: function() {
                    hideNPSNSuggestions();
                }
            });
        }, 300);
    });

    // Hide suggestions when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#npsn, .npsn-suggestions').length) {
            hideNPSNSuggestions();
        }
    });

    // Validasi manual untuk NPSN (ketika user menekan Enter atau blur)
    $('#npsn').on('blur keypress', function(e) {
        if (e.type === 'keypress' && e.which !== 13) return; // Only on Enter or blur

        const npsn = $(this).val().trim();
        const sekolah_id = $('#sekolah_id').val().trim();

        if (npsn && !sekolah_id) {
            // Coba validasi manual
            validateNPSNManual(npsn);
        }
    });
}

/**
 * Validasi NPSN secara manual (exact match)
 */
function validateNPSNManual(npsn) {
    $.ajax({
        url: 'ajax/autocomplete_npsn.php',
        type: 'POST',
        data: { search: npsn },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                // Cari exact match
                const exactMatch = response.data.find(item => item.npsn === npsn);
                if (exactMatch) {
                    selectNPSN(exactMatch);
                } else {
                    // Tidak ada exact match
                    $('#sekolah-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NPSN tidak ditemukan, silakan pilih dari dropdown');
                }
            } else {
                // Tidak ada data ditemukan
                $('#sekolah-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NPSN tidak ditemukan');
            }
        },
        error: function() {
            $('#sekolah-info').html('<i class="fas fa-exclamation-circle text-danger"></i> Error validasi NPSN');
        }
    });
}

/**
 * Tampilkan suggestions NPSN
 */
function showNPSNSuggestions(data) {
    hideNPSNSuggestions();

    const suggestions = $('<div class="npsn-suggestions"></div>');
    suggestions.css({
        'position': 'absolute',
        'top': '100%',
        'left': '0',
        'right': '0',
        'background': 'white',
        'border': '1px solid #ddd',
        'border-top': 'none',
        'max-height': '200px',
        'overflow-y': 'auto',
        'z-index': '1000',
        'box-shadow': '0 2px 4px rgba(0,0,0,0.1)'
    });

    data.forEach(function(item) {
        const option = $('<div class="suggestion-item"></div>');
        option.css({
            'padding': '8px 12px',
            'cursor': 'pointer',
            'border-bottom': '1px solid #eee'
        });

        option.html('<strong>' + item.npsn + '</strong><br>' +
                   '<small>' + item.nama_sekolah + ' (' + item.jenjang + ' - ' + item.kota + ')</small>');

        option.on('click', function() {
            selectNPSN(item);
        });

        option.on('mouseenter', function() {
            $(this).css('background-color', '#f8f9fa');
        }).on('mouseleave', function() {
            $(this).css('background-color', 'white');
        });

        suggestions.append(option);
    });

    $('#npsn').parent().css('position', 'relative').append(suggestions);
}

/**
 * Sembunyikan suggestions NPSN
 */
function hideNPSNSuggestions() {
    $('.npsn-suggestions').remove();
}

/**
 * Pilih NPSN dari suggestions
 */
function selectNPSN(item) {
    $('#npsn').val(item.npsn);
    $('#sekolah_id').val(item.sekolah_id);
    $('#sekolah-info').html('<i class="fas fa-check text-success"></i> ' +
                           item.nama_sekolah + ' (' + item.jenjang + ' - ' + item.kota + ')');
    hideNPSNSuggestions();
}

/**
 * Inisialisasi autocomplete untuk NIA
 */
function initNIAAutocomplete() {
    let niaTimeout1, niaTimeout2;

    // Autocomplete untuk NIA Validator 1
    $('#nia_validator1').on('input', function() {
        const search = $(this).val().trim();

        clearTimeout(niaTimeout1);
        $('#kd_asesor1').val('');
        $('#asesor1-info').text('');

        if (search.length < 2) {
            hideNIASuggestions('nia_validator1');
            return;
        }

        niaTimeout1 = setTimeout(function() {
            $.ajax({
                url: 'ajax/autocomplete_nia.php',
                type: 'POST',
                data: { search: search, type: 'asesor1' },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        showNIASuggestions(response.data, 'nia_validator1', 'asesor1');
                    } else {
                        hideNIASuggestions('nia_validator1');
                        $('#asesor1-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan');
                    }
                },
                error: function() {
                    hideNIASuggestions('nia_validator1');
                    $('#asesor1-info').html('<i class="fas fa-exclamation-circle text-danger"></i> Error mencari data');
                }
            });
        }, 300);
    });

    // Autocomplete untuk NIA Validator 2
    $('#nia_validator2').on('input', function() {
        const search = $(this).val().trim();

        clearTimeout(niaTimeout2);
        $('#kd_asesor2').val('');
        $('#asesor2-info').text('');

        if (search.length < 2) {
            hideNIASuggestions('nia_validator2');
            return;
        }

        niaTimeout2 = setTimeout(function() {
            $.ajax({
                url: 'ajax/autocomplete_nia.php',
                type: 'POST',
                data: { search: search, type: 'asesor2' },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        showNIASuggestions(response.data, 'nia_validator2', 'asesor2');
                    } else {
                        hideNIASuggestions('nia_validator2');
                        $('#asesor2-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan');
                    }
                },
                error: function() {
                    hideNIASuggestions('nia_validator2');
                    $('#asesor2-info').html('<i class="fas fa-exclamation-circle text-danger"></i> Error mencari data');
                }
            });
        }, 300);
    });

    // Hide suggestions when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#nia_validator1, #nia_validator2, .nia-suggestions').length) {
            hideNIASuggestions('nia_validator1');
            hideNIASuggestions('nia_validator2');
        }
    });

    // Validasi manual untuk NIA (ketika user menekan Enter atau blur)
    $('#nia_validator1').on('blur keypress', function(e) {
        if (e.type === 'keypress' && e.which !== 13) return; // Only on Enter or blur

        const nia = $(this).val().trim();
        const kd_asesor = $('#kd_asesor1').val().trim();

        if (nia && !kd_asesor) {
            // Coba validasi manual
            validateNIAManual(nia, 'asesor1', 'nia_validator1');
        }
    });

    $('#nia_validator2').on('blur keypress', function(e) {
        if (e.type === 'keypress' && e.which !== 13) return; // Only on Enter or blur

        const nia = $(this).val().trim();
        const kd_asesor = $('#kd_asesor2').val().trim();

        if (nia && !kd_asesor) {
            // Coba validasi manual
            validateNIAManual(nia, 'asesor2', 'nia_validator2');
        }
    });
}

/**
 * Validasi NIA secara manual (exact match)
 */
function validateNIAManual(nia, type, inputId) {
    $.ajax({
        url: 'ajax/autocomplete_nia.php',
        type: 'POST',
        data: { search: nia, type: type },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                // Cari exact match
                const exactMatch = response.data.find(item => item.nia === nia);
                if (exactMatch) {
                    selectNIA(exactMatch, inputId, type);
                } else {
                    // Tidak ada exact match
                    if (type === 'asesor1') {
                        $('#asesor1-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan, silakan pilih dari dropdown');
                    } else {
                        $('#asesor2-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan, silakan pilih dari dropdown');
                    }
                }
            } else {
                // Tidak ada data ditemukan
                if (type === 'asesor1') {
                    $('#asesor1-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan');
                } else {
                    $('#asesor2-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan');
                }
            }
        },
        error: function() {
            if (type === 'asesor1') {
                $('#asesor1-info').html('<i class="fas fa-exclamation-circle text-danger"></i> Error validasi NIA');
            } else {
                $('#asesor2-info').html('<i class="fas fa-exclamation-circle text-danger"></i> Error validasi NIA');
            }
        }
    });
}

/**
 * Tampilkan suggestions NIA
 */
function showNIASuggestions(data, inputId, type) {
    hideNIASuggestions(inputId);

    const suggestions = $('<div class="nia-suggestions"></div>');
    suggestions.css({
        'position': 'absolute',
        'top': '100%',
        'left': '0',
        'right': '0',
        'background': 'white',
        'border': '1px solid #ddd',
        'border-top': 'none',
        'max-height': '200px',
        'overflow-y': 'auto',
        'z-index': '1000',
        'box-shadow': '0 2px 4px rgba(0,0,0,0.1)'
    });

    data.forEach(function(item) {
        const option = $('<div class="suggestion-item"></div>');
        option.css({
            'padding': '8px 12px',
            'cursor': 'pointer',
            'border-bottom': '1px solid #eee'
        });

        option.html('<strong>' + item.nia + '</strong><br>' +
                   '<small>' + item.nama_asesor + ' (' + item.unit_kerja + ')</small>');

        option.on('click', function() {
            selectNIA(item, inputId, type);
        });

        option.on('mouseenter', function() {
            $(this).css('background-color', '#f8f9fa');
        }).on('mouseleave', function() {
            $(this).css('background-color', 'white');
        });

        suggestions.append(option);
    });

    $('#' + inputId).parent().css('position', 'relative').append(suggestions);
}

/**
 * Sembunyikan suggestions NIA
 */
function hideNIASuggestions(inputId) {
    $('#' + inputId).parent().find('.nia-suggestions').remove();
}

/**
 * Pilih NIA dari suggestions
 */
function selectNIA(item, inputId, type) {
    $('#' + inputId).val(item.nia);

    if (type === 'asesor1') {
        $('#kd_asesor1').val(item.kd_asesor);
        $('#asesor1-info').html('<i class="fas fa-check text-success"></i> ' +
                               item.nama_asesor + ' (' + item.unit_kerja + ')');
    } else {
        $('#kd_asesor2').val(item.kd_asesor);
        $('#asesor2-info').html('<i class="fas fa-check text-success"></i> ' +
                               item.nama_asesor + ' (' + item.unit_kerja + ')');
    }

    hideNIASuggestions(inputId);
}

/**
 * Submit form data mapping
 */
function submitForm() {
    // Validasi form
    if (!validateForm()) {
        return;
    }

    // Disable tombol submit
    $('#btn-simpan').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Ambil data form
    const formData = {
        npsn: $('#npsn').val(),
        nia_validator1: $('#nia_validator1').val(),
        nia_validator2: $('#nia_validator2').val(),
        tahun_akreditasi: $('#tahun_akreditasi').val(),
        tahap: $('#tahap').val(),
        tgl_mulai_validasi: $('#tgl_mulai_validasi').val(),
        tgl_akhir_validasi: $('#tgl_akhir_validasi').val(),
        tgl_surat_validasi: $('#tgl_surat_validasi').val(),
        no_surat_validasi: $('#no_surat_validasi').val()
    };

    // AJAX request
    $.ajax({
        url: 'ajax/simpan_mapping.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-tambah').modal('hide');

                // Reload DataTable
                $('#table-validasi').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message dalam modal
                showModalAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showModalAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-simpan').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

/**
 * Validasi form sebelum submit
 */
function validateForm() {
    let isValid = true;

    // Clear previous alerts
    $('#modal-alert-container').html('');

    // Validasi NPSN dan sekolah_id
    const npsn = $('#npsn').val().trim();
    const sekolah_id = $('#sekolah_id').val().trim();

    if (!npsn) {
        showModalAlert('error', 'NPSN Sekolah harus diisi');
        isValid = false;
    } else if (!sekolah_id) {
        showModalAlert('error', 'NPSN Sekolah harus dipilih dari daftar yang tersedia. Silakan ketik ulang dan pilih dari dropdown.');
        isValid = false;
    }

    // Validasi NIA Validator 1
    const nia1 = $('#nia_validator1').val().trim();
    const kd_asesor1 = $('#kd_asesor1').val().trim();

    if (!nia1) {
        showModalAlert('error', 'NIA Validator 1 harus diisi');
        isValid = false;
    } else if (!kd_asesor1) {
        showModalAlert('error', 'NIA Validator 1 harus dipilih dari daftar yang tersedia. Silakan ketik ulang dan pilih dari dropdown.');
        isValid = false;
    }

    // Validasi NIA Validator 2
    const nia2 = $('#nia_validator2').val().trim();
    const kd_asesor2 = $('#kd_asesor2').val().trim();

    if (!nia2) {
        showModalAlert('error', 'NIA Validator 2 harus diisi');
        isValid = false;
    } else if (!kd_asesor2) {
        showModalAlert('error', 'NIA Validator 2 harus dipilih dari daftar yang tersedia. Silakan ketik ulang dan pilih dari dropdown.');
        isValid = false;
    }

    // Validasi tahun akreditasi
    if (!$('#tahun_akreditasi').val().trim()) {
        showModalAlert('error', 'Tahun Akreditasi harus diisi');
        isValid = false;
    }

    // Validasi tahap
    if (!$('#tahap').val().trim()) {
        showModalAlert('error', 'Tahap Ke harus diisi');
        isValid = false;
    }

    // Validasi NIA tidak boleh sama
    if (nia1 && nia2 && nia1 === nia2) {
        showModalAlert('error', 'NIA Validator 1 dan Validator 2 tidak boleh sama');
        isValid = false;
    }

    // Debug information (akan dihapus setelah testing)
    if (!isValid) {
        console.log('Validation failed:');
        console.log('NPSN:', npsn, 'Sekolah ID:', sekolah_id);
        console.log('NIA1:', nia1, 'Kd Asesor1:', kd_asesor1);
        console.log('NIA2:', nia2, 'Kd Asesor2:', kd_asesor2);
    }

    return isValid;
}

/**
 * Fungsi untuk menampilkan alert dalam modal
 */
function showModalAlert(type, message) {
    var alertClass = '';
    var iconClass = '';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            iconClass = 'fas fa-check-circle';
            break;
        case 'error':
            alertClass = 'alert-danger';
            iconClass = 'fas fa-exclamation-circle';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            iconClass = 'fas fa-exclamation-triangle';
            break;
        case 'info':
        default:
            alertClass = 'alert-info';
            iconClass = 'fas fa-info-circle';
            break;
    }

    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    '<i class="' + iconClass + '"></i> ' + message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';

    $('#modal-alert-container').html(alertHtml);

    // Auto hide after 5 seconds
    setTimeout(function() {
        $('#modal-alert-container .alert').fadeOut('slow', function() {
            $(this).remove();
        });
    }, 5000);
}

/**
 * Load dropdown filter tahun akreditasi
 */
function loadDropdownFilterTahun() {
    $.ajax({
        url: 'ajax/get_tahun_akreditasi.php',
        type: 'GET',
        data: { action: 'get' },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var dropdown = $('#dropdown-filter-tahun');
                dropdown.empty();

                // Tambahkan option "Semua Tahun"
                dropdown.append('<a class="dropdown-item" href="#" data-tahun="">Semua Tahun</a>');

                if (response.data.length > 0) {
                    dropdown.append('<div class="dropdown-divider"></div>');

                    // Tambahkan tahun dari database
                    response.data.forEach(function(item) {
                        dropdown.append('<a class="dropdown-item" href="#" data-tahun="' + item.tahun + '">' + item.tahun + '</a>');
                    });
                } else {
                    dropdown.append('<div class="dropdown-divider"></div>');
                    dropdown.append('<div class="dropdown-item-text text-muted">Belum ada data tahun</div>');
                }
            } else {
                $('#dropdown-filter-tahun').html('<div class="dropdown-item-text text-danger">Error memuat data</div>');
            }
        },
        error: function() {
            $('#dropdown-filter-tahun').html('<div class="dropdown-item-text text-danger">Error memuat data</div>');
        }
    });
}

/**
 * Buka modal tahun akreditasi
 */
function openModalTahunAkreditasi() {
    // Ambil tahun akreditasi saat ini
    $.ajax({
        url: 'ajax/get_tahun_akreditasi.php',
        type: 'GET',
        data: { action: 'get' },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Set nilai tahun saat ini ke form
                $('#tahun-akreditasi').val(response.current_tahun);
                $('#modal-tahun-akreditasi').modal('show');
            } else {
                showAlert('error', 'Gagal memuat data tahun akreditasi');
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat memuat data tahun');
        }
    });
}

/**
 * Tampilkan status filter
 */
function showFilterStatus(filterText) {
    $('#filter-text').text(filterText);
    $('#filter-status').show();
}

/**
 * Hapus filter dan refresh tabel
 */
function clearFilter() {
    // Clear filter tahun
    $('#filter-tahun').val('');

    // Update dropdown text
    $('#filter-tahun-text').text('Semua Tahun');

    // Hide filter status
    $('#filter-status').hide();

    // Refresh DataTable tanpa filter
    $('#table-validasi').DataTable().ajax.reload(null, false);

    showAlert('info', 'Filter tahun akreditasi telah dihapus');
}

// Global functions untuk akses dari HTML
window.detailValidasi = detailValidasi;
window.reloadDataTable = reloadDataTable;
window.showAlert = showAlert;
window.openModalTahunAkreditasi = openModalTahunAkreditasi;
