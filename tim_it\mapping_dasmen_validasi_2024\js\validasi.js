/**
 * JavaScript untuk Mapping Asesor Validasi SM
 */

$(document).ready(function() {
    // Inisialisasi DataTable
    initDataTable();

    // Event handlers
    initEventHandlers();
});

/**
 * Inisialisasi DataTable
 */
function initDataTable() {
    $('#table-validasi').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_validasi.php',
            type: 'POST',
            error: function(xhr, error, thrown) {
                console.error('DataTables error:', error);
                showAlert('error', 'Terjadi kesalahan saat memuat data');
            }
        },
        columns: [
            { data: 0, name: 'no', orderable: false, searchable: false, className: 'text-center' },
            { data: 1, name: 'npsn', className: 'text-center' },
            { data: 2, name: 'nama_sekolah' },
            { data: 3, name: 'jenjang', className: 'text-center' },
            { data: 4, name: 'rumpun', className: 'text-center' },
            { data: 5, name: 'kab_kota' },
            { data: 6, name: 'nia1', className: 'text-center' },
            { data: 7, name: 'nama_asesor1' },
            { data: 8, name: 'nia2', className: 'text-center' },
            { data: 9, name: 'nama_asesor2' },
            { data: 10, name: 'tahun_akreditasi', className: 'text-center' },
            { data: 11, name: 'tahap', className: 'text-center', orderable: false },
            { data: 12, name: 'aksi', orderable: false, searchable: false, className: 'text-center' }
        ],
        order: [[1, 'asc']], // Order by NPSN
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            infoPostFix: "",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        responsive: true,
        autoWidth: false,
        scrollX: true,
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        drawCallback: function(settings) {
            // Inisialisasi tooltip setelah tabel digambar
            $('[data-toggle="tooltip"]').tooltip();
        }
    });
}

/**
 * Inisialisasi Event Handlers
 */
function initEventHandlers() {
    // Tombol Input Data Mapping
    $('#btn-add').on('click', function() {
        resetForm();
        $('#modal-tambah').modal('show');
    });

    // Tombol Export Excel
    $('#btn-export').on('click', function() {
        showAlert('info', 'Fitur export Excel akan ditambahkan pada tahap selanjutnya');
        // exportToExcel();
    });

    // Tombol Import Excel
    $('#btn-import').on('click', function() {
        showAlert('info', 'Fitur import Excel akan ditambahkan pada tahap selanjutnya');
        // $('#modal-import').modal('show');
    });

    // Tombol Tahun Akreditasi
    $('#btn-tahun-akreditasi').on('click', function() {
        openModalTahunAkreditasi();
    });

    // Event handler untuk tombol simpan tahun
    $('#btn-simpan-tahun').on('click', function() {
        var tahun = $('#tahun-akreditasi').val();

        if (!tahun || tahun < 2020 || tahun > 2030) {
            showAlert('warning', 'Tahun harus antara 2020-2030');
            return;
        }

        // Simpan tahun akreditasi
        $.ajax({
            url: 'ajax/get_tahun_akreditasi.php',
            type: 'POST',
            data: {
                action: 'update',
                tahun_akreditasi: tahun
            },
            dataType: 'json',
            beforeSend: function() {
                $('#btn-simpan-tahun').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
            },
            success: function(response) {
                if (response.success) {
                    $('#modal-tahun-akreditasi').modal('hide');

                    // Refresh DataTable tanpa reload halaman
                    $('#table-validasi').DataTable().ajax.reload(null, false);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function() {
                showAlert('error', 'Terjadi kesalahan saat menyimpan tahun');
            },
            complete: function() {
                $('#btn-simpan-tahun').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan');
            }
        });
    });

    // Form submit handler
    $('#form-tambah').on('submit', function(e) {
        e.preventDefault();
        submitForm();
    });

    // Autocomplete untuk NPSN
    initNPSNAutocomplete();

    // Autocomplete untuk NIA Validator 1 dan 2
    initNIAAutocomplete();

    // Event handlers untuk tombol aksi di modal detail
    $('#btn-edit-validator').on('click', function() {
        openModalEditValidator();
    });

    $('#btn-edit-tanggal').on('click', function() {
        openModalEditTanggal();
    });

    $('#btn-hapus-mapping').on('click', function() {
        hapusMapping();
    });

    $('#btn-download-st').on('click', function() {
        downloadSuratTugas();
    });

    // Event handler untuk tombol update tanggal
    $('#btn-update-tanggal').on('click', function() {
        updateTanggalValidasi();
    });

    // Event handler untuk tombol update validator
    $('#btn-update-validator').on('click', function() {
        updateValidator();
    });

    // Refresh DataTable ketika modal ditutup
    $('#modal-tambah, #modal-import, #modal-tahun-akreditasi, #modal-detail-validasi').on('hidden.bs.modal', function() {
        $('#table-validasi').DataTable().ajax.reload(null, false);
    });

    // Event handler untuk modal edit tanggal
    $('#modal-edit-tanggal').on('hidden.bs.modal', function() {
        // Refresh DataTable untuk update tanggal di tabel utama
        $('#table-validasi').DataTable().ajax.reload(null, false);
    });

    // Event handler untuk modal edit validator
    $('#modal-edit-validator').on('hidden.bs.modal', function() {
        // Refresh DataTable untuk update validator di tabel utama
        $('#table-validasi').DataTable().ajax.reload(null, false);
    });
}

/**
 * Fungsi untuk menampilkan detail validasi
 */
function detailValidasi(idMapping) {
    // Validasi parameter
    if (!idMapping) {
        showAlert('error', 'ID mapping tidak valid');
        return;
    }

    // AJAX request untuk mengambil detail data
    $.ajax({
        url: 'ajax/get_detail_validasi.php',
        type: 'POST',
        data: { id_mapping: idMapping },
        dataType: 'json',
        beforeSend: function() {
            // Show loading di modal
            $('#modal-detail-validasi').modal('show');
            showLoadingDetail();
        },
        success: function(response) {
            if (response.success) {
                populateDetailModal(response.data);
            } else {
                showAlert('error', response.message);
                $('#modal-detail-validasi').modal('hide');
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat memuat detail data');
            $('#modal-detail-validasi').modal('hide');
        }
    });
}

/**
 * Fungsi untuk menampilkan alert
 */
function showAlert(type, message) {
    var alertClass = '';
    var iconClass = '';
    
    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            iconClass = 'fas fa-check-circle';
            break;
        case 'error':
            alertClass = 'alert-danger';
            iconClass = 'fas fa-exclamation-circle';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            iconClass = 'fas fa-exclamation-triangle';
            break;
        case 'info':
        default:
            alertClass = 'alert-info';
            iconClass = 'fas fa-info-circle';
            break;
    }
    
    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    '<i class="' + iconClass + '"></i> ' + message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';
    
    $('#alert-container').html(alertHtml);
    
    // Auto hide after 5 seconds
    setTimeout(function() {
        $('#alert-container .alert').fadeOut('slow', function() {
            $(this).remove();
        });
    }, 5000);
}

/**
 * Fungsi untuk reload DataTable
 */
function reloadDataTable() {
    if ($.fn.DataTable.isDataTable('#table-validasi')) {
        $('#table-validasi').DataTable().ajax.reload(null, false);
    } else {
        initDataTable();
    }
}

/**
 * Fungsi placeholder untuk export Excel
 */
function exportToExcel() {
    // Placeholder - akan diimplementasikan nanti
    showAlert('info', 'Fungsi export Excel sedang dalam pengembangan');
}

/**
 * Fungsi placeholder untuk import Excel
 */
function importFromExcel() {
    // Placeholder - akan diimplementasikan nanti
    showAlert('info', 'Fungsi import Excel sedang dalam pengembangan');
}

/**
 * Reset form input data mapping
 */
function resetForm() {
    $('#form-tambah')[0].reset();
    $('#sekolah_id').val('');
    $('#kd_asesor1').val('');
    $('#kd_asesor2').val('');
    $('#sekolah-info').text('');
    $('#asesor1-info').text('');
    $('#asesor2-info').text('');
    $('#modal-alert-container').html('');
}

/**
 * Inisialisasi autocomplete untuk NPSN
 */
function initNPSNAutocomplete() {
    let npsnTimeout;

    $('#npsn').on('input', function() {
        const search = $(this).val().trim();

        // Clear previous timeout
        clearTimeout(npsnTimeout);

        // Clear previous data
        $('#sekolah_id').val('');
        $('#sekolah-info').text('');

        if (search.length < 2) {
            return;
        }

        // Set timeout untuk menghindari terlalu banyak request
        npsnTimeout = setTimeout(function() {
            $.ajax({
                url: 'ajax/autocomplete_npsn.php',
                type: 'POST',
                data: { search: search },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        showNPSNSuggestions(response.data);
                    } else {
                        hideNPSNSuggestions();
                    }
                },
                error: function() {
                    hideNPSNSuggestions();
                }
            });
        }, 300);
    });

    // Hide suggestions when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#npsn, .npsn-suggestions').length) {
            hideNPSNSuggestions();
        }
    });

    // Validasi manual untuk NPSN (ketika user menekan Enter atau blur)
    $('#npsn').on('blur keypress', function(e) {
        if (e.type === 'keypress' && e.which !== 13) return; // Only on Enter or blur

        const npsn = $(this).val().trim();
        const sekolah_id = $('#sekolah_id').val().trim();

        if (npsn && !sekolah_id) {
            // Coba validasi manual
            validateNPSNManual(npsn);
        }
    });
}

/**
 * Validasi NPSN secara manual (exact match)
 */
function validateNPSNManual(npsn) {
    $.ajax({
        url: 'ajax/autocomplete_npsn.php',
        type: 'POST',
        data: { search: npsn },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                // Cari exact match
                const exactMatch = response.data.find(item => item.npsn === npsn);
                if (exactMatch) {
                    selectNPSN(exactMatch);
                } else {
                    // Tidak ada exact match
                    $('#sekolah-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NPSN tidak ditemukan, silakan pilih dari dropdown');
                }
            } else {
                // Tidak ada data ditemukan
                $('#sekolah-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NPSN tidak ditemukan');
            }
        },
        error: function() {
            $('#sekolah-info').html('<i class="fas fa-exclamation-circle text-danger"></i> Error validasi NPSN');
        }
    });
}

/**
 * Tampilkan suggestions NPSN
 */
function showNPSNSuggestions(data) {
    hideNPSNSuggestions();

    const suggestions = $('<div class="npsn-suggestions"></div>');
    suggestions.css({
        'position': 'absolute',
        'top': '100%',
        'left': '0',
        'right': '0',
        'background': 'white',
        'border': '1px solid #ddd',
        'border-top': 'none',
        'max-height': '200px',
        'overflow-y': 'auto',
        'z-index': '1000',
        'box-shadow': '0 2px 4px rgba(0,0,0,0.1)'
    });

    data.forEach(function(item) {
        const option = $('<div class="suggestion-item"></div>');
        option.css({
            'padding': '8px 12px',
            'cursor': 'pointer',
            'border-bottom': '1px solid #eee'
        });

        option.html('<strong>' + item.npsn + '</strong><br>' +
                   '<small>' + item.nama_sekolah + ' (' + item.jenjang + ' - ' + item.kota + ')</small>');

        option.on('click', function() {
            selectNPSN(item);
        });

        option.on('mouseenter', function() {
            $(this).css('background-color', '#f8f9fa');
        }).on('mouseleave', function() {
            $(this).css('background-color', 'white');
        });

        suggestions.append(option);
    });

    $('#npsn').parent().css('position', 'relative').append(suggestions);
}

/**
 * Sembunyikan suggestions NPSN
 */
function hideNPSNSuggestions() {
    $('.npsn-suggestions').remove();
}

/**
 * Pilih NPSN dari suggestions
 */
function selectNPSN(item) {
    $('#npsn').val(item.npsn);
    $('#sekolah_id').val(item.sekolah_id);
    $('#sekolah-info').html('<i class="fas fa-check text-success"></i> ' +
                           item.nama_sekolah + ' (' + item.jenjang + ' - ' + item.kota + ')');
    hideNPSNSuggestions();
}

/**
 * Inisialisasi autocomplete untuk NIA
 */
function initNIAAutocomplete() {
    let niaTimeout1, niaTimeout2;

    // Autocomplete untuk NIA Validator 1
    $('#nia_validator1').on('input', function() {
        const search = $(this).val().trim();

        clearTimeout(niaTimeout1);
        $('#kd_asesor1').val('');
        $('#asesor1-info').text('');

        if (search.length < 2) {
            hideNIASuggestions('nia_validator1');
            return;
        }

        niaTimeout1 = setTimeout(function() {
            $.ajax({
                url: 'ajax/autocomplete_nia.php',
                type: 'POST',
                data: { search: search, type: 'asesor1' },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        showNIASuggestions(response.data, 'nia_validator1', 'asesor1');
                    } else {
                        hideNIASuggestions('nia_validator1');
                        $('#asesor1-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan');
                    }
                },
                error: function() {
                    hideNIASuggestions('nia_validator1');
                    $('#asesor1-info').html('<i class="fas fa-exclamation-circle text-danger"></i> Error mencari data');
                }
            });
        }, 300);
    });

    // Autocomplete untuk NIA Validator 2
    $('#nia_validator2').on('input', function() {
        const search = $(this).val().trim();

        clearTimeout(niaTimeout2);
        $('#kd_asesor2').val('');
        $('#asesor2-info').text('');

        if (search.length < 2) {
            hideNIASuggestions('nia_validator2');
            return;
        }

        niaTimeout2 = setTimeout(function() {
            $.ajax({
                url: 'ajax/autocomplete_nia.php',
                type: 'POST',
                data: { search: search, type: 'asesor2' },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        showNIASuggestions(response.data, 'nia_validator2', 'asesor2');
                    } else {
                        hideNIASuggestions('nia_validator2');
                        $('#asesor2-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan');
                    }
                },
                error: function() {
                    hideNIASuggestions('nia_validator2');
                    $('#asesor2-info').html('<i class="fas fa-exclamation-circle text-danger"></i> Error mencari data');
                }
            });
        }, 300);
    });

    // Hide suggestions when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#nia_validator1, #nia_validator2, .nia-suggestions').length) {
            hideNIASuggestions('nia_validator1');
            hideNIASuggestions('nia_validator2');
        }
    });

    // Validasi manual untuk NIA (ketika user menekan Enter atau blur)
    $('#nia_validator1').on('blur keypress', function(e) {
        if (e.type === 'keypress' && e.which !== 13) return; // Only on Enter or blur

        const nia = $(this).val().trim();
        const kd_asesor = $('#kd_asesor1').val().trim();

        if (nia && !kd_asesor) {
            // Coba validasi manual
            validateNIAManual(nia, 'asesor1', 'nia_validator1');
        }
    });

    $('#nia_validator2').on('blur keypress', function(e) {
        if (e.type === 'keypress' && e.which !== 13) return; // Only on Enter or blur

        const nia = $(this).val().trim();
        const kd_asesor = $('#kd_asesor2').val().trim();

        if (nia && !kd_asesor) {
            // Coba validasi manual
            validateNIAManual(nia, 'asesor2', 'nia_validator2');
        }
    });
}

/**
 * Validasi NIA secara manual (exact match)
 */
function validateNIAManual(nia, type, inputId) {
    $.ajax({
        url: 'ajax/autocomplete_nia.php',
        type: 'POST',
        data: { search: nia, type: type },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                // Cari exact match
                const exactMatch = response.data.find(item => item.nia === nia);
                if (exactMatch) {
                    selectNIA(exactMatch, inputId, type);
                } else {
                    // Tidak ada exact match
                    if (type === 'asesor1') {
                        $('#asesor1-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan, silakan pilih dari dropdown');
                    } else {
                        $('#asesor2-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan, silakan pilih dari dropdown');
                    }
                }
            } else {
                // Tidak ada data ditemukan
                if (type === 'asesor1') {
                    $('#asesor1-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan');
                } else {
                    $('#asesor2-info').html('<i class="fas fa-exclamation-triangle text-warning"></i> NIA tidak ditemukan');
                }
            }
        },
        error: function() {
            if (type === 'asesor1') {
                $('#asesor1-info').html('<i class="fas fa-exclamation-circle text-danger"></i> Error validasi NIA');
            } else {
                $('#asesor2-info').html('<i class="fas fa-exclamation-circle text-danger"></i> Error validasi NIA');
            }
        }
    });
}

/**
 * Tampilkan suggestions NIA
 */
function showNIASuggestions(data, inputId, type) {
    hideNIASuggestions(inputId);

    const suggestions = $('<div class="nia-suggestions"></div>');
    suggestions.css({
        'position': 'absolute',
        'top': '100%',
        'left': '0',
        'right': '0',
        'background': 'white',
        'border': '1px solid #ddd',
        'border-top': 'none',
        'max-height': '200px',
        'overflow-y': 'auto',
        'z-index': '1000',
        'box-shadow': '0 2px 4px rgba(0,0,0,0.1)'
    });

    data.forEach(function(item) {
        const option = $('<div class="suggestion-item"></div>');
        option.css({
            'padding': '8px 12px',
            'cursor': 'pointer',
            'border-bottom': '1px solid #eee'
        });

        option.html('<strong>' + item.nia + '</strong><br>' +
                   '<small>' + item.nama_asesor + ' (' + item.unit_kerja + ')</small>');

        option.on('click', function() {
            selectNIA(item, inputId, type);
        });

        option.on('mouseenter', function() {
            $(this).css('background-color', '#f8f9fa');
        }).on('mouseleave', function() {
            $(this).css('background-color', 'white');
        });

        suggestions.append(option);
    });

    $('#' + inputId).parent().css('position', 'relative').append(suggestions);
}

/**
 * Sembunyikan suggestions NIA
 */
function hideNIASuggestions(inputId) {
    $('#' + inputId).parent().find('.nia-suggestions').remove();
}

/**
 * Pilih NIA dari suggestions
 */
function selectNIA(item, inputId, type) {
    $('#' + inputId).val(item.nia);

    if (type === 'asesor1') {
        $('#kd_asesor1').val(item.kd_asesor);
        $('#asesor1-info').html('<i class="fas fa-check text-success"></i> ' +
                               item.nama_asesor + ' (' + item.unit_kerja + ')');
    } else {
        $('#kd_asesor2').val(item.kd_asesor);
        $('#asesor2-info').html('<i class="fas fa-check text-success"></i> ' +
                               item.nama_asesor + ' (' + item.unit_kerja + ')');
    }

    hideNIASuggestions(inputId);
}

/**
 * Submit form data mapping
 */
function submitForm() {
    // Validasi form
    if (!validateForm()) {
        return;
    }

    // Disable tombol submit
    $('#btn-simpan').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Ambil data form
    const formData = {
        npsn: $('#npsn').val(),
        nia_validator1: $('#nia_validator1').val(),
        nia_validator2: $('#nia_validator2').val(),
        tahun_akreditasi: $('#tahun_akreditasi').val(),
        tahap: $('#tahap').val(),
        tgl_mulai_validasi: $('#tgl_mulai_validasi').val(),
        tgl_akhir_validasi: $('#tgl_akhir_validasi').val(),
        tgl_surat_validasi: $('#tgl_surat_validasi').val(),
        no_surat_validasi: $('#no_surat_validasi').val()
    };

    // AJAX request
    $.ajax({
        url: 'ajax/simpan_mapping.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-tambah').modal('hide');

                // Reload DataTable
                $('#table-validasi').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message dalam modal
                showModalAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showModalAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-simpan').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

/**
 * Validasi form sebelum submit
 */
function validateForm() {
    let isValid = true;

    // Clear previous alerts
    $('#modal-alert-container').html('');

    // Validasi NPSN dan sekolah_id
    const npsn = $('#npsn').val().trim();
    const sekolah_id = $('#sekolah_id').val().trim();

    if (!npsn) {
        showModalAlert('error', 'NPSN Sekolah harus diisi');
        isValid = false;
    } else if (!sekolah_id) {
        showModalAlert('error', 'NPSN Sekolah harus dipilih dari daftar yang tersedia. Silakan ketik ulang dan pilih dari dropdown.');
        isValid = false;
    }

    // Validasi NIA Validator 1
    const nia1 = $('#nia_validator1').val().trim();
    const kd_asesor1 = $('#kd_asesor1').val().trim();

    if (!nia1) {
        showModalAlert('error', 'NIA Validator 1 harus diisi');
        isValid = false;
    } else if (!kd_asesor1) {
        showModalAlert('error', 'NIA Validator 1 harus dipilih dari daftar yang tersedia. Silakan ketik ulang dan pilih dari dropdown.');
        isValid = false;
    }

    // Validasi NIA Validator 2
    const nia2 = $('#nia_validator2').val().trim();
    const kd_asesor2 = $('#kd_asesor2').val().trim();

    if (!nia2) {
        showModalAlert('error', 'NIA Validator 2 harus diisi');
        isValid = false;
    } else if (!kd_asesor2) {
        showModalAlert('error', 'NIA Validator 2 harus dipilih dari daftar yang tersedia. Silakan ketik ulang dan pilih dari dropdown.');
        isValid = false;
    }

    // Validasi tahun akreditasi
    if (!$('#tahun_akreditasi').val().trim()) {
        showModalAlert('error', 'Tahun Akreditasi harus diisi');
        isValid = false;
    }

    // Validasi tahap
    if (!$('#tahap').val().trim()) {
        showModalAlert('error', 'Tahap Ke harus diisi');
        isValid = false;
    }

    // Validasi NIA tidak boleh sama
    if (nia1 && nia2 && nia1 === nia2) {
        showModalAlert('error', 'NIA Validator 1 dan Validator 2 tidak boleh sama');
        isValid = false;
    }

    // Debug information (akan dihapus setelah testing)
    if (!isValid) {
        console.log('Validation failed:');
        console.log('NPSN:', npsn, 'Sekolah ID:', sekolah_id);
        console.log('NIA1:', nia1, 'Kd Asesor1:', kd_asesor1);
        console.log('NIA2:', nia2, 'Kd Asesor2:', kd_asesor2);
    }

    return isValid;
}

/**
 * Fungsi untuk menampilkan alert dalam modal
 */
function showModalAlert(type, message) {
    var alertClass = '';
    var iconClass = '';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            iconClass = 'fas fa-check-circle';
            break;
        case 'error':
            alertClass = 'alert-danger';
            iconClass = 'fas fa-exclamation-circle';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            iconClass = 'fas fa-exclamation-triangle';
            break;
        case 'info':
        default:
            alertClass = 'alert-info';
            iconClass = 'fas fa-info-circle';
            break;
    }

    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    '<i class="' + iconClass + '"></i> ' + message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';

    $('#modal-alert-container').html(alertHtml);

    // Auto hide after 5 seconds
    setTimeout(function() {
        $('#modal-alert-container .alert').fadeOut('slow', function() {
            $(this).remove();
        });
    }, 5000);
}



/**
 * Buka modal tahun akreditasi
 */
function openModalTahunAkreditasi() {
    // Ambil tahun akreditasi saat ini
    $.ajax({
        url: 'ajax/get_tahun_akreditasi.php',
        type: 'GET',
        data: { action: 'get' },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Set nilai tahun saat ini ke form
                $('#tahun-akreditasi').val(response.current_tahun);
                $('#modal-tahun-akreditasi').modal('show');
            } else {
                showAlert('error', 'Gagal memuat data tahun akreditasi');
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat memuat data tahun');
        }
    });
}



/**
 * Tampilkan loading di modal detail
 */
function showLoadingDetail() {
    const loadingText = '<i class="fas fa-spinner fa-spin"></i> Memuat...';

    // Data Sekolah
    $('#detail-npsn').html(loadingText);
    $('#detail-nama-sekolah').html(loadingText);
    $('#detail-jenjang').html(loadingText);
    $('#detail-kab-kota').html(loadingText);
    $('#detail-nama-kepsek').html(loadingText);
    $('#detail-hp-kepsek').html(loadingText);
    $('#detail-wa-kepsek').html(loadingText);

    // Data Asesor
    $('#detail-nia1').html(loadingText);
    $('#detail-nama-asesor1').html(loadingText);
    $('#detail-hp-asesor1').html(loadingText);
    $('#detail-kota-asesor1').html(loadingText);
    $('#detail-nia2').html(loadingText);
    $('#detail-nama-asesor2').html(loadingText);
    $('#detail-hp-asesor2').html(loadingText);
    $('#detail-kota-asesor2').html(loadingText);

    // Dokumen Unggahan
    $('#detail-file-ba1').html(loadingText);
    $('#detail-file-ba2').html(loadingText);
    $('#detail-file-pakta1').html(loadingText);
    $('#detail-file-pakta2').html(loadingText);
    $('#detail-file-st').html(loadingText);

    // Pelaksanaan Kegiatan
    $('#detail-tgl-mulai').html(loadingText);
    $('#detail-tgl-akhir').html(loadingText);
    $('#detail-no-st').html(loadingText);
    $('#detail-tgl-st').html(loadingText);
    $('#detail-tahap').html(loadingText);
}

/**
 * Populate data ke modal detail
 */
function populateDetailModal(data) {
    // Data Sekolah
    $('#detail-npsn').text(data.npsn);
    $('#detail-nama-sekolah').text(data.nama_sekolah);
    $('#detail-jenjang').text(data.jenjang);
    $('#detail-kab-kota').text(data.kab_kota);
    $('#detail-nama-kepsek').text(data.nama_kepsek);
    $('#detail-hp-kepsek').text(data.hp_kepsek);
    $('#detail-wa-kepsek').text(data.wa_kepsek);

    // Data Asesor
    $('#detail-nia1').text(data.nia1);
    $('#detail-nama-asesor1').text(data.nama_asesor1);
    $('#detail-hp-asesor1').text(data.hp_asesor1);
    $('#detail-kota-asesor1').text(data.kota_asesor1);
    $('#detail-nia2').text(data.nia2);
    $('#detail-nama-asesor2').text(data.nama_asesor2);
    $('#detail-hp-asesor2').text(data.hp_asesor2);
    $('#detail-kota-asesor2').text(data.kota_asesor2);

    // Dokumen Unggahan (HTML karena ada badge)
    $('#detail-file-ba1').html(data.file_ba1);
    $('#detail-file-ba2').html(data.file_ba2);
    $('#detail-file-pakta1').html(data.file_pakta1);
    $('#detail-file-pakta2').html(data.file_pakta2);
    $('#detail-file-st').html(data.file_st);

    // Pelaksanaan Kegiatan
    $('#detail-tgl-mulai').text(data.tgl_mulai);
    $('#detail-tgl-akhir').text(data.tgl_akhir);
    $('#detail-no-st').text(data.no_st);
    $('#detail-tgl-st').text(data.tgl_st);
    $('#detail-tahap').text(data.tahap);

    // Store ID mapping untuk tombol aksi
    $('#modal-detail-validasi').data('id-mapping', data.id_mapping);

    // Store raw tanggal untuk edit form
    $('#modal-detail-validasi').data('tgl-mulai-raw', data.tgl_mulai_raw || '');
    $('#modal-detail-validasi').data('tgl-akhir-raw', data.tgl_akhir_raw || '');

    // Store NIA untuk edit validator form
    $('#modal-detail-validasi').data('nia1', data.nia1);
    $('#modal-detail-validasi').data('nia2', data.nia2);
}

/**
 * Buka modal edit tanggal validasi
 */
function openModalEditTanggal() {
    const idMapping = $('#modal-detail-validasi').data('id-mapping');
    const tglMulaiRaw = $('#modal-detail-validasi').data('tgl-mulai-raw');
    const tglAkhirRaw = $('#modal-detail-validasi').data('tgl-akhir-raw');

    if (!idMapping) {
        showAlert('error', 'ID mapping tidak ditemukan');
        return;
    }

    // Set form values
    $('#edit-id-mapping').val(idMapping);
    $('#edit-tgl-mulai').val(tglMulaiRaw === '0000-00-00' ? '' : tglMulaiRaw);
    $('#edit-tgl-akhir').val(tglAkhirRaw === '0000-00-00' ? '' : tglAkhirRaw);

    // Show modal edit tanggal
    $('#modal-edit-tanggal').modal('show');
}

/**
 * Buka modal edit validator
 */
function openModalEditValidator() {
    const idMapping = $('#modal-detail-validasi').data('id-mapping');
    const nia1 = $('#modal-detail-validasi').data('nia1');
    const nia2 = $('#modal-detail-validasi').data('nia2');

    if (!idMapping) {
        showAlert('error', 'ID mapping tidak ditemukan');
        return;
    }

    // Set form values
    $('#edit-validator-id-mapping').val(idMapping);
    $('#edit-nia1').val(nia1 === '-' ? '' : nia1);
    $('#edit-nia2').val(nia2 === '-' ? '' : nia2);

    // Setup autocomplete untuk NIA
    setupEditNiaAutocomplete();

    // Show modal edit validator
    $('#modal-edit-validator').modal('show');
}

/**
 * Setup autocomplete untuk edit NIA
 */
function setupEditNiaAutocomplete() {
    // Autocomplete untuk NIA Asesor 1
    $('#edit-nia1').on('input', function() {
        const query = $(this).val();
        if (query.length >= 2) {
            searchNia(query, 'edit-nia1-suggestions', 'edit-nia1', 'asesor1');
        } else {
            $('#edit-nia1-suggestions').hide();
        }
    });

    // Autocomplete untuk NIA Asesor 2
    $('#edit-nia2').on('input', function() {
        const query = $(this).val();
        if (query.length >= 2) {
            searchNia(query, 'edit-nia2-suggestions', 'edit-nia2', 'asesor2');
        } else {
            $('#edit-nia2-suggestions').hide();
        }
    });

    // Hide suggestions saat click di luar
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#edit-nia1, #edit-nia1-suggestions').length) {
            $('#edit-nia1-suggestions').hide();
        }
        if (!$(e.target).closest('#edit-nia2, #edit-nia2-suggestions').length) {
            $('#edit-nia2-suggestions').hide();
        }
    });
}

/**
 * Update validator
 */
function updateValidator() {
    const formData = {
        id_mapping: $('#edit-validator-id-mapping').val(),
        nia1: $('#edit-nia1').val(),
        nia2: $('#edit-nia2').val()
    };

    // Validasi form
    if (!formData.id_mapping) {
        showAlert('error', 'ID mapping tidak valid');
        return;
    }

    // AJAX request
    $.ajax({
        url: 'ajax/update_validator.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        beforeSend: function() {
            $('#btn-update-validator').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');
        },
        success: function(response) {
            if (response.success) {
                // Update display di modal detail tanpa reload
                $('#detail-nia1').text(response.data.nia1);
                $('#detail-nama-asesor1').text(response.data.nama_asesor1);
                $('#detail-hp-asesor1').text(response.data.hp_asesor1);
                $('#detail-kota-asesor1').text(response.data.kota_asesor1);
                $('#detail-nia2').text(response.data.nia2);
                $('#detail-nama-asesor2').text(response.data.nama_asesor2);
                $('#detail-hp-asesor2').text(response.data.hp_asesor2);
                $('#detail-kota-asesor2').text(response.data.kota_asesor2);

                // Update stored NIA data untuk edit selanjutnya
                $('#modal-detail-validasi').data('nia1', response.data.nia1);
                $('#modal-detail-validasi').data('nia2', response.data.nia2);

                // Close modal edit
                $('#modal-edit-validator').modal('hide');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat update validator');
        },
        complete: function() {
            $('#btn-update-validator').prop('disabled', false).html('<i class="fas fa-save"></i> Update Perubahan');
        }
    });
}

/**
 * Update tanggal validasi
 */
function updateTanggalValidasi() {
    const formData = {
        id_mapping: $('#edit-id-mapping').val(),
        tgl_mulai_validasi: $('#edit-tgl-mulai').val(),
        tgl_akhir_validasi: $('#edit-tgl-akhir').val()
    };

    // Validasi form
    if (!formData.id_mapping) {
        showAlert('error', 'ID mapping tidak valid');
        return;
    }

    // AJAX request
    $.ajax({
        url: 'ajax/update_tanggal_validasi.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        beforeSend: function() {
            $('#btn-update-tanggal').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');
        },
        success: function(response) {
            if (response.success) {
                // Update display di modal detail tanpa reload
                $('#detail-tgl-mulai').text(response.data.tgl_mulai);
                $('#detail-tgl-akhir').text(response.data.tgl_akhir);

                // Update raw data untuk edit selanjutnya
                $('#modal-detail-validasi').data('tgl-mulai-raw', response.data.tgl_mulai_raw || '');
                $('#modal-detail-validasi').data('tgl-akhir-raw', response.data.tgl_akhir_raw || '');

                // Close modal edit
                $('#modal-edit-tanggal').modal('hide');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat update tanggal validasi');
        },
        complete: function() {
            $('#btn-update-tanggal').prop('disabled', false).html('<i class="fas fa-save"></i> Update Perubahan');
        }
    });
}

/**
 * Hapus mapping validasi
 */
function hapusMapping() {
    const idMapping = $('#modal-detail-validasi').data('id-mapping');

    if (!idMapping) {
        showAlert('error', 'ID mapping tidak ditemukan');
        return;
    }

    // Konfirmasi penghapusan
    if (!confirm('Apakah Anda yakin ingin menghapus mapping validasi ini?\n\nData yang dihapus tidak dapat dikembalikan!')) {
        return;
    }

    // AJAX request untuk hapus
    $.ajax({
        url: 'ajax/hapus_mapping.php',
        type: 'POST',
        data: { id_mapping: idMapping },
        dataType: 'json',
        beforeSend: function() {
            $('#btn-hapus-mapping').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menghapus...');
        },
        success: function(response) {
            if (response.success) {
                // Tutup modal detail
                $('#modal-detail-validasi').modal('hide');

                // Refresh DataTable untuk menghapus baris dari tabel
                $('#table-validasi').DataTable().ajax.reload(null, false);

                // Tampilkan pesan sukses
                showAlert('success', response.message);
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menghapus mapping validasi');
        },
        complete: function() {
            $('#btn-hapus-mapping').prop('disabled', false).html('<i class="fas fa-trash"></i> Hapus');
        }
    });
}

/**
 * Download Surat Tugas Validasi
 */
function downloadSuratTugas() {
    const idMapping = $('#modal-detail-validasi').data('id-mapping');

    if (!idMapping) {
        showAlert('error', 'ID mapping tidak ditemukan');
        return;
    }

    // Buka file PDF surat tugas di tab baru
    const url = 'mapping_validasi_st_validasi_2024.php?kode=' + idMapping;
    window.open(url, '_blank');
}

/**
 * Search NIA untuk autocomplete
 */
function searchNia(query, suggestionsId, inputId, asesorType) {
    $.ajax({
        url: 'ajax/search_nia.php',
        type: 'GET',
        data: {
            q: query,
            type: asesorType
        },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                let suggestions = '<div class="list-group">';
                response.data.forEach(function(item) {
                    suggestions += `<a href="#" class="list-group-item list-group-item-action"
                                     onclick="selectNia('${item.nia}', '${item.nama}', '${inputId}', '${suggestionsId}')">
                                     <strong>${item.nia}</strong> - ${item.nama}
                                   </a>`;
                });
                suggestions += '</div>';

                $('#' + suggestionsId).html(suggestions).show();
            } else {
                $('#' + suggestionsId).hide();
            }
        },
        error: function() {
            $('#' + suggestionsId).hide();
        }
    });
}

/**
 * Select NIA dari autocomplete
 */
function selectNia(nia, nama, inputId, suggestionsId) {
    $('#' + inputId).val(nia);
    $('#' + suggestionsId).hide();
}

// Global functions untuk akses dari HTML
window.detailValidasi = detailValidasi;
window.reloadDataTable = reloadDataTable;
window.showAlert = showAlert;
window.openModalTahunAkreditasi = openModalTahunAkreditasi;
window.selectNia = selectNia;
window.downloadSuratTugas = downloadSuratTugas;
window.hapusMapping = hapusMapping;
