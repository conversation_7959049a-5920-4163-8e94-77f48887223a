<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

// Include header
include '../header.php';
?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-clipboard-check"></i> Mapping Asesor Validasi SM
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard/dashboard.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="#">Mapping Dasmen Validasi 2024</a></li>
                        <li class="breadcrumb-item active">Validasi SM</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <!-- Alert untuk notifikasi -->
            <div id="alert-container"></div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list"></i> Daftar Mapping Asesor Validasi SM
                            </h3>
                            <div class="card-tools">

                                <!-- Tombol Input Data Mapping -->
                                <button type="button" class="btn btn-primary btn-sm" id="btn-add">
                                    <i class="fas fa-plus"></i> Input Data Mapping
                                </button>
                                
                                <!-- Tombol Export Excel -->
                                <button type="button" class="btn btn-success btn-sm mr-2" id="btn-export">
                                    <i class="fas fa-file-excel"></i> Export Excel
                                </button>

                                <!-- Tombol Import Excel -->
                                <button type="button" class="btn btn-warning btn-sm mr-2" id="btn-import">
                                    <i class="fas fa-file-import"></i> Import Excel
                                </button>

                                <!-- Tombol Tahun Akreditasi -->
                                <button type="button" class="btn btn-warning btn-sm mr-2" id="btn-tahun-akreditasi">
                                    <i class="fas fa-calendar-alt"></i> Tahun Akreditasi
                                </button>
                                
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="table-validasi" class="table table-bordered table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th width="3%">NO <br> &nbsp; <br> &nbsp; </th>
                                            <th width="8%">NPSN<br> &nbsp; <br> &nbsp;  </th>
                                            <th width="20%">NAMA <br> SEKOLAH <br> &nbsp; </th>
                                            <th width="8%">JENJANG <br> &nbsp; <br> &nbsp;  </th>
                                            <th width="8%">RUMPUN <br> &nbsp; <br> &nbsp;  </th>
                                            <th width="12%">KAB/KOTA <br> &nbsp; <br> &nbsp;  </th>
                                            <th width="8%">NIA <br> VALIDATOR <br> 1</th>
                                            <th width="15%">NAMA <br> VALIDATOR <br> 1</th>
                                            <th width="8%">NIA <br> VALIDATOR <br> 2</th>
                                            <th width="15%">NAMA <br> VALIDATOR <br> 2</th>
                                            <th width="6%">TAHUN <br> AKREDITASI <br> &nbsp; </th>
                                            <th width="6%">TAHAP <br> VALIDASI <br> &nbsp; </th>
                                            <th width="8%">AKSI<br> &nbsp; <br> &nbsp; </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan dimuat via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal untuk Input Data Mapping -->
<div class="modal fade" id="modal-tambah" tabindex="-1" role="dialog" aria-labelledby="modal-tambah-label" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modal-tambah-label">
                    <i class="fas fa-plus"></i> Input Data Mapping Validasi SM
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-tambah" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <!-- Kolom Kiri -->
                        <div class="col-md-6">
                            <!-- NPSN Sekolah -->
                            <div class="form-group">
                                <label for="npsn">NPSN Sekolah <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="npsn" name="npsn" required
                                           placeholder="Ketik NPSN untuk mencari sekolah..." autocomplete="off">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    </div>
                                </div>
                                <input type="hidden" id="sekolah_id" name="sekolah_id">
                                <small class="form-text text-muted" id="sekolah-info"></small>
                            </div>

                            <!-- NIA Validator 1 -->
                            <div class="form-group">
                                <label for="nia_validator1">NIA Validator 1 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="nia_validator1" name="nia_validator1" required
                                           placeholder="Ketik NIA untuk mencari asesor..." autocomplete="off">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    </div>
                                </div>
                                <input type="hidden" id="kd_asesor1" name="kd_asesor1">
                                <small class="form-text text-muted" id="asesor1-info"></small>
                            </div>

                            <!-- NIA Validator 2 -->
                            <div class="form-group">
                                <label for="nia_validator2">NIA Validator 2 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="nia_validator2" name="nia_validator2" required
                                           placeholder="Ketik NIA untuk mencari asesor..." autocomplete="off">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    </div>
                                </div>
                                <input type="hidden" id="kd_asesor2" name="kd_asesor2">
                                <small class="form-text text-muted" id="asesor2-info"></small>
                            </div>

                            <!-- Tahun Akreditasi -->
                            <div class="form-group">
                                <label for="tahun_akreditasi">Tahun Akreditasi <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="tahun_akreditasi" name="tahun_akreditasi" required
                                       placeholder="Contoh: 2024" maxlength="4">
                                <small class="form-text text-muted">Masukkan tahun akreditasi (4 digit)</small>
                            </div>

                            <!-- Tahap Ke -->
                            <div class="form-group">
                                <label for="tahap">Tahap Ke <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="tahap" name="tahap" required
                                       placeholder="Contoh: 1" min="1">
                                <small class="form-text text-muted">Masukkan tahap validasi</small>
                            </div>
                        </div>

                        <!-- Kolom Kanan -->
                        <div class="col-md-6">
                            <!-- Tanggal Mulai Validasi -->
                            <div class="form-group">
                                <label for="tgl_mulai_validasi">Tanggal Mulai Validasi</label>
                                <input type="date" class="form-control" id="tgl_mulai_validasi" name="tgl_mulai_validasi">
                                <small class="form-text text-muted">Tanggal dimulainya proses validasi</small>
                            </div>

                            <!-- Tanggal Akhir Validasi -->
                            <div class="form-group">
                                <label for="tgl_akhir_validasi">Tanggal Akhir Validasi</label>
                                <input type="date" class="form-control" id="tgl_akhir_validasi" name="tgl_akhir_validasi">
                                <small class="form-text text-muted">Tanggal berakhirnya proses validasi</small>
                            </div>

                            <!-- Tanggal Surat Tugas Validasi -->
                            <div class="form-group">
                                <label for="tgl_surat_validasi">Tanggal Surat Tugas Validasi</label>
                                <input type="date" class="form-control" id="tgl_surat_validasi" name="tgl_surat_validasi">
                                <small class="form-text text-muted">Tanggal surat tugas validasi</small>
                            </div>

                            <!-- Nomor Surat Tugas Validasi -->
                            <div class="form-group">
                                <label for="no_surat_validasi">Nomor Surat Tugas Validasi</label>
                                <input type="text" class="form-control" id="no_surat_validasi" name="no_surat_validasi"
                                       placeholder="Contoh: 001/ST/2024" maxlength="30">
                                <small class="form-text text-muted">Nomor surat tugas validasi</small>
                            </div>
                        </div>
                    </div>

                    <!-- Alert untuk notifikasi dalam modal -->
                    <div id="modal-alert-container"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-primary" id="btn-simpan">
                        <i class="fas fa-save"></i> Simpan Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal untuk Import Excel (placeholder) -->
<div class="modal fade" id="modal-import" tabindex="-1" role="dialog" aria-labelledby="modal-import-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="modal-import-label">
                    <i class="fas fa-file-import"></i> Import Data Excel
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Form import akan ditambahkan nanti -->
                <p class="text-center text-muted">Form import akan ditambahkan pada tahap selanjutnya</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Tahun Akreditasi -->
<div class="modal fade" id="modal-tahun-akreditasi" tabindex="-1" role="dialog" aria-labelledby="modal-tahun-akreditasi-label" aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="modal-tahun-akreditasi-label">
                    <i class="fas fa-calendar-alt"></i> Edit Tahun Akreditasi
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form-tahun-akreditasi">
                    <div class="form-group">
                        <label for="tahun-akreditasi">Tahun Akreditasi</label>
                        <input type="number" class="form-control" id="tahun-akreditasi" name="tahun_akreditasi"
                               min="2020" max="2030" placeholder="2024" required>
                        <small class="form-text text-muted">Format: YYYY (contoh: 2024)</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-warning btn-sm" id="btn-simpan-tahun">
                    <i class="fas fa-save"></i> Simpan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- JavaScript -->
<script src="js/validasi.js"></script>

<style>
/* Custom CSS untuk tabel */
#table-validasi {
    font-size: 13px;
}

#table-validasi th {
    background-color: #343a40;
    color: white;
    text-align: center;
    vertical-align: middle;
    font-weight: 600;
    font-size: 12px;
}

#table-validasi td {
    vertical-align: middle;
    font-size: 12px;
}

.btn-sm {
    font-size: 11px;
    padding: 0.25rem 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-tools {
        margin-top: 10px;
    }
    
    .btn-group {
        margin-bottom: 5px;
    }
}
</style>
