<?php
/**
 * AJAX handler untuk mengambil data mapping validasi SM
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Parameter DataTables
    $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
    $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
    $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
    $search_value = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';
    
    // Parameter filter tahun
    $filter_tahun = isset($_POST['filter_tahun']) ? $_POST['filter_tahun'] : '';
    
    // Kolom untuk ordering
    $columns = [
        0 => 'mv.id_mapping',
        1 => 's.npsn',
        2 => 's.nama_sekolah',
        3 => 'j.nm_jenjang',
        4 => 's.rumpun',
        5 => 'k.nm_kota',
        6 => 'a1.nia1',
        7 => 'a1.nm_asesor1',
        8 => 'a2.nia2',
        9 => 'a2.nm_asesor2',
        10 => 'mv.tahun_akreditasi',
        11 => 'mv.tahap'
    ];
    
    $order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 0;
    $order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'asc';
    $order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'mv.id_mapping';
    
    // Base query dengan JOIN ke semua tabel terkait
    $base_query = "FROM mapping_validasi_2024 mv
                   LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                   LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
                   LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
                   WHERE mv.provinsi_id = ? AND s.rumpun = 'dasmen'";
    
    // Array untuk parameter
    $params = [$provinsi_id_session];
    $param_types = "i";
    
    // Filter berdasarkan tahun akreditasi
    if (!empty($filter_tahun)) {
        $base_query .= " AND mv.tahun_akreditasi = ?";
        $params[] = $filter_tahun;
        $param_types .= "s";
    }
    
    // Filter untuk jenjang SM (SMA dan SMK)
    $base_query .= " AND (j.jenjang_id = '3' OR j.jenjang_id = '4' OR j.nm_jenjang LIKE '%SMA%' OR j.nm_jenjang LIKE '%SMK%')";
    
    // Search functionality
    $search_conditions = [];
    if (!empty($search_value)) {
        $search_conditions = [
            "s.npsn LIKE ?",
            "s.nama_sekolah LIKE ?",
            "j.nm_jenjang LIKE ?",
            "s.rumpun LIKE ?",
            "k.nm_kota LIKE ?",
            "a1.nia1 LIKE ?",
            "a1.nm_asesor1 LIKE ?",
            "a2.nia2 LIKE ?",
            "a2.nm_asesor2 LIKE ?",
            "mv.tahun_akreditasi LIKE ?",
            "mv.tahap LIKE ?"
        ];
        
        $base_query .= " AND (" . implode(" OR ", $search_conditions) . ")";
        
        // Tambahkan parameter search
        for ($i = 0; $i < count($search_conditions); $i++) {
            $params[] = "%{$search_value}%";
            $param_types .= "s";
        }
    }
    
    // Query untuk total records tanpa filter
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $stmt_total = $conn->prepare($total_query);
    $stmt_total->bind_param($param_types, ...$params);
    $stmt_total->execute();
    $total_records = $stmt_total->get_result()->fetch_assoc()['total'];
    
    // Query untuk filtered records (sama dengan total jika ada search)
    $filtered_records = $total_records;
    
    // Query untuk data dengan pagination
    $data_query = "SELECT mv.id_mapping,
                          s.npsn,
                          s.nama_sekolah,
                          j.nm_jenjang,
                          s.rumpun,
                          k.nm_kota,
                          a1.nia1,
                          a1.nm_asesor1,
                          a2.nia2,
                          a2.nm_asesor2,
                          mv.tahun_akreditasi,
                          mv.tahap,
                          mv.tgl_mulai_validasi,
                          mv.tgl_akhir_validasi,
                          mv.no_surat_validasi,
                          mv.tgl_surat_validasi
                   " . $base_query . "
                   ORDER BY {$order_by} {$order_dir}
                   LIMIT ?, ?";
    
    // Tambahkan parameter untuk LIMIT
    $params[] = $start;
    $params[] = $length;
    $param_types .= "ii";
    
    $stmt_data = $conn->prepare($data_query);
    $stmt_data->bind_param($param_types, ...$params);
    $stmt_data->execute();
    $result = $stmt_data->get_result();
    
    $data = [];
    $no = $start + 1;
    
    while ($row = $result->fetch_assoc()) {
        // Format tahap
        $tahap_text = '';
        switch ($row['tahap']) {
            case 1:
                $tahap_text = '<span class="badge badge-info">Tahap 1</span>';
                break;
            case 2:
                $tahap_text = '<span class="badge badge-warning">Tahap 2</span>';
                break;
            case 3:
                $tahap_text = '<span class="badge badge-success">Selesai</span>';
                break;
            default:
                $tahap_text = '<span class="badge badge-secondary">-</span>';
        }
        
        // Tombol aksi
        $aksi = '<div class="btn-group" role="group">
                    <button type="button" class="btn btn-info btn-sm" onclick="detailValidasi(' . $row['id_mapping'] . ')" title="Detail">
                        <i class="fas fa-eye"></i>
                    </button>
                 </div>';
        
        $data[] = [
            $no++,
            $row['npsn'] ?? '-',
            $row['nama_sekolah'] ?? '-',
            $row['nm_jenjang'] ?? '-',
            $row['rumpun'] ?? '-',
            $row['nm_kota'] ?? '-',
            $row['nia1'] ?? '-',
            $row['nm_asesor1'] ?? '-',
            $row['nia2'] ?? '-',
            $row['nm_asesor2'] ?? '-',
            $row['tahun_akreditasi'] ?? '-',
            $tahap_text,
            $aksi
        ];
    }
    
    // Response untuk DataTables
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Error response
    $response = [
        'draw' => isset($draw) ? $draw : 1,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Terjadi kesalahan: ' . $e->getMessage()
    ];
    
    echo json_encode($response);
    error_log("Error in get_validasi.php: " . $e->getMessage());
}
?>
