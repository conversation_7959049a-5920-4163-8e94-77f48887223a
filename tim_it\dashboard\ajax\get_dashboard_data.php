<?php
/**
 * AJAX handler untuk mengambil data dashboard real
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // 1. Total sekolah rumpun dasmen aktif per jenjang
    $query_dasmen = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
                     FROM sekolah s
                     LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                     WHERE s.provinsi_id = ?
                     AND s.rumpun = 'dasmen'
                     AND s.status_keaktifan_id = '1'
                     AND (s.soft_delete IS NULL OR s.soft_delete != '1')
                     GROUP BY j.jenjang_id, j.nm_jenjang
                     ORDER BY j.jenjang_id";
    
    $stmt_dasmen = $conn->prepare($query_dasmen);
    $stmt_dasmen->bind_param("i", $provinsi_id);
    $stmt_dasmen->execute();
    $result_dasmen = $stmt_dasmen->get_result();
    
    $data_dasmen = [];
    $total_dasmen = 0;
    while ($row = $result_dasmen->fetch_assoc()) {
        $data_dasmen[] = $row;
        $total_dasmen += $row['total'];
    }
    
    // 2. Total sekolah rumpun paud aktif per jenjang
    $query_paud = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
                   FROM sekolah s
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   WHERE s.provinsi_id = ? 
                   AND s.rumpun = 'paud'
                   AND s.status_keaktifan_id = '1'
                   AND (s.soft_delete IS NULL OR s.soft_delete != '1')
                   GROUP BY j.jenjang_id, j.nm_jenjang
                   ORDER BY j.jenjang_id";
    
    $stmt_paud = $conn->prepare($query_paud);
    $stmt_paud->bind_param("i", $provinsi_id);
    $stmt_paud->execute();
    $result_paud = $stmt_paud->get_result();
    
    $data_paud = [];
    $total_paud = 0;
    while ($row = $result_paud->fetch_assoc()) {
        $data_paud[] = $row;
        $total_paud += $row['total'];
    }
    
    // 3. Total sekolah rumpun kesetaraan aktif per jenjang
    $query_kesetaraan = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
                         FROM sekolah s
                         LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                         WHERE s.provinsi_id = ? 
                         AND s.rumpun = 'kesetaraan'
                         AND s.status_keaktifan_id = '1'
                         AND (s.soft_delete IS NULL OR s.soft_delete != '1')
                         GROUP BY j.jenjang_id, j.nm_jenjang
                         ORDER BY j.jenjang_id";
    
    $stmt_kesetaraan = $conn->prepare($query_kesetaraan);
    $stmt_kesetaraan->bind_param("i", $provinsi_id);
    $stmt_kesetaraan->execute();
    $result_kesetaraan = $stmt_kesetaraan->get_result();
    
    $data_kesetaraan = [];
    $total_kesetaraan = 0;
    while ($row = $result_kesetaraan->fetch_assoc()) {
        $data_kesetaraan[] = $row;
        $total_kesetaraan += $row['total'];
    }
    
    // 4. Total peringkat akreditasi per rumpun
    $query_akreditasi_rumpun = "SELECT s.rumpun, ha.peringkat, COUNT(*) as total
                                FROM hasil_akreditasi ha
                                JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                                WHERE ha.provinsi_id = ?
                                AND ha.peringkat IN ('A', 'B', 'C')
                                AND s.status_keaktifan_id = '1'
                                AND (s.soft_delete IS NULL OR s.soft_delete != '1')
                                GROUP BY s.rumpun, ha.peringkat
                                ORDER BY s.rumpun, ha.peringkat";
    
    $stmt_akreditasi_rumpun = $conn->prepare($query_akreditasi_rumpun);
    $stmt_akreditasi_rumpun->bind_param("i", $provinsi_id);
    $stmt_akreditasi_rumpun->execute();
    $result_akreditasi_rumpun = $stmt_akreditasi_rumpun->get_result();
    
    $data_akreditasi_rumpun = [];
    while ($row = $result_akreditasi_rumpun->fetch_assoc()) {
        $data_akreditasi_rumpun[] = $row;
    }
    
    // 5. Total peringkat akreditasi per jenjang
    $query_akreditasi_jenjang = "SELECT j.nm_jenjang, ha.peringkat, COUNT(*) as total
                                 FROM hasil_akreditasi ha
                                 JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                                 JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                                 WHERE ha.provinsi_id = ?
                                 AND ha.peringkat IN ('A', 'B', 'C')
                                 AND s.status_keaktifan_id = '1'
                                 AND (s.soft_delete IS NULL OR s.soft_delete != '1')
                                 GROUP BY j.jenjang_id, j.nm_jenjang, ha.peringkat
                                 ORDER BY j.jenjang_id, ha.peringkat";
    
    $stmt_akreditasi_jenjang = $conn->prepare($query_akreditasi_jenjang);
    $stmt_akreditasi_jenjang->bind_param("i", $provinsi_id);
    $stmt_akreditasi_jenjang->execute();
    $result_akreditasi_jenjang = $stmt_akreditasi_jenjang->get_result();
    
    $data_akreditasi_jenjang = [];
    while ($row = $result_akreditasi_jenjang->fetch_assoc()) {
        $data_akreditasi_jenjang[] = $row;
    }
    
    // 6. Total peringkat akreditasi per kabupaten/kota (top 10)
    $query_akreditasi_kota = "SELECT kk.nm_kota, ha.peringkat, COUNT(*) as total
                              FROM hasil_akreditasi ha
                              JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                              JOIN kab_kota kk ON s.kota_id = kk.kota_id
                              WHERE ha.provinsi_id = ?
                              AND ha.peringkat IN ('A', 'B', 'C')
                              AND s.status_keaktifan_id = '1'
                              AND (s.soft_delete IS NULL OR s.soft_delete != '1')
                              GROUP BY kk.kota_id, kk.nm_kota, ha.peringkat
                              ORDER BY kk.nm_kota, ha.peringkat";
    
    $stmt_akreditasi_kota = $conn->prepare($query_akreditasi_kota);
    $stmt_akreditasi_kota->bind_param("i", $provinsi_id);
    $stmt_akreditasi_kota->execute();
    $result_akreditasi_kota = $stmt_akreditasi_kota->get_result();
    
    $data_akreditasi_kota = [];
    while ($row = $result_akreditasi_kota->fetch_assoc()) {
        $data_akreditasi_kota[] = $row;
    }
    
    // Response
    echo json_encode([
        'success' => true,
        'data' => [
            'sekolah_dasmen' => $data_dasmen,
            'total_dasmen' => $total_dasmen,
            'sekolah_paud' => $data_paud,
            'total_paud' => $total_paud,
            'sekolah_kesetaraan' => $data_kesetaraan,
            'total_kesetaraan' => $total_kesetaraan,
            'akreditasi_rumpun' => $data_akreditasi_rumpun,
            'akreditasi_jenjang' => $data_akreditasi_jenjang,
            'akreditasi_kota' => $data_akreditasi_kota
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
    error_log("Error in get_dashboard_data.php: " . $e->getMessage());
}

$conn->close();
?>
