<?php
/**
 * AJAX handler untuk menyimpan data mapping validasi SM
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['npsn', 'nia_validator1', 'nia_validator2', 'tahun_akreditasi', 'tahap'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }
    
    // Ambil data input
    $npsn = trim($_POST['npsn']);
    $nia_validator1 = trim($_POST['nia_validator1']);
    $nia_validator2 = trim($_POST['nia_validator2']);
    $tahun_akreditasi = trim($_POST['tahun_akreditasi']);
    $tahap = intval($_POST['tahap']);
    
    // Optional fields
    $tgl_mulai_validasi = !empty($_POST['tgl_mulai_validasi']) ? $_POST['tgl_mulai_validasi'] : null;
    $tgl_akhir_validasi = !empty($_POST['tgl_akhir_validasi']) ? $_POST['tgl_akhir_validasi'] : null;
    $tgl_surat_validasi = !empty($_POST['tgl_surat_validasi']) ? $_POST['tgl_surat_validasi'] : null;
    $no_surat_validasi = !empty($_POST['no_surat_validasi']) ? trim($_POST['no_surat_validasi']) : null;
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Begin transaction
    $conn->autocommit(false);
    
    // 1. Validasi dan lookup NPSN ke sekolah_id
    $sql_sekolah = "SELECT sekolah_id, nama_sekolah FROM sekolah 
                    WHERE npsn = ? AND provinsi_id = ? AND soft_delete = '1' AND status_keaktifan_id = '1'";
    $stmt_sekolah = $conn->prepare($sql_sekolah);
    $stmt_sekolah->bind_param("si", $npsn, $provinsi_id_session);
    $stmt_sekolah->execute();
    $result_sekolah = $stmt_sekolah->get_result();
    
    if ($result_sekolah->num_rows == 0) {
        throw new Exception('NPSN tidak ditemukan atau tidak sesuai dengan provinsi Anda');
    }
    
    $sekolah_data = $result_sekolah->fetch_assoc();
    $sekolah_id = $sekolah_data['sekolah_id'];
    $nama_sekolah = $sekolah_data['nama_sekolah'];
    
    // 2. Validasi dan lookup NIA Validator 1 ke kd_asesor1
    $sql_asesor1 = "SELECT kd_asesor1, nm_asesor1 FROM asesor_1 
                    WHERE nia1 = ? AND soft_delete = '1'";
    $stmt_asesor1 = $conn->prepare($sql_asesor1);
    $stmt_asesor1->bind_param("s", $nia_validator1);
    $stmt_asesor1->execute();
    $result_asesor1 = $stmt_asesor1->get_result();
    
    if ($result_asesor1->num_rows == 0) {
        throw new Exception('NIA Validator 1 tidak ditemukan di database asesor_1');
    }
    
    $asesor1_data = $result_asesor1->fetch_assoc();
    $kd_asesor1 = $asesor1_data['kd_asesor1'];
    $nm_asesor1 = $asesor1_data['nm_asesor1'];
    
    // 3. Validasi dan lookup NIA Validator 2 ke kd_asesor2
    $sql_asesor2 = "SELECT kd_asesor2, nm_asesor2 FROM asesor_2 
                    WHERE nia2 = ? AND soft_delete = '1'";
    $stmt_asesor2 = $conn->prepare($sql_asesor2);
    $stmt_asesor2->bind_param("s", $nia_validator2);
    $stmt_asesor2->execute();
    $result_asesor2 = $stmt_asesor2->get_result();
    
    if ($result_asesor2->num_rows == 0) {
        throw new Exception('NIA Validator 2 tidak ditemukan di database asesor_2');
    }
    
    $asesor2_data = $result_asesor2->fetch_assoc();
    $kd_asesor2 = $asesor2_data['kd_asesor2'];
    $nm_asesor2 = $asesor2_data['nm_asesor2'];
    
    // 4. Validasi duplikasi mapping (satu sekolah tidak boleh di-mapping lebih dari sekali untuk tahun yang sama)
    $sql_duplicate = "SELECT COUNT(*) as count FROM mapping_validasi_2024 
                      WHERE sekolah_id = ? AND tahun_akreditasi = ? AND provinsi_id = ?";
    $stmt_duplicate = $conn->prepare($sql_duplicate);
    $stmt_duplicate->bind_param("isi", $sekolah_id, $tahun_akreditasi, $provinsi_id_session);
    $stmt_duplicate->execute();
    $duplicate_result = $stmt_duplicate->get_result();
    $duplicate_count = $duplicate_result->fetch_assoc()['count'];
    
    if ($duplicate_count > 0) {
        throw new Exception('Sekolah "' . $nama_sekolah . '" sudah di-mapping untuk tahun akreditasi ' . $tahun_akreditasi);
    }
    
    // 5. Insert data mapping
    $sql_insert = "INSERT INTO mapping_validasi_2024 
                   (sekolah_id, kd_asesor1, kd_asesor2, tgl_mulai_validasi, tgl_akhir_validasi, 
                    tgl_surat_validasi, no_surat_validasi, tahun_akreditasi, tahap, provinsi_id) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt_insert = $conn->prepare($sql_insert);
    $stmt_insert->bind_param("isssssssii", 
        $sekolah_id, $kd_asesor1, $kd_asesor2, $tgl_mulai_validasi, $tgl_akhir_validasi,
        $tgl_surat_validasi, $no_surat_validasi, $tahun_akreditasi, $tahap, $provinsi_id_session
    );
    
    if (!$stmt_insert->execute()) {
        throw new Exception('Gagal menyimpan data mapping: ' . $stmt_insert->error);
    }
    
    $id_mapping = $conn->insert_id;
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data mapping validasi berhasil disimpan untuk sekolah "' . $nama_sekolah . '"',
        'data' => [
            'id_mapping' => $id_mapping,
            'sekolah_id' => $sekolah_id,
            'nama_sekolah' => $nama_sekolah,
            'npsn' => $npsn,
            'kd_asesor1' => $kd_asesor1,
            'nm_asesor1' => $nm_asesor1,
            'kd_asesor2' => $kd_asesor2,
            'nm_asesor2' => $nm_asesor2,
            'tahun_akreditasi' => $tahun_akreditasi,
            'tahap' => $tahap
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback transaction
    $conn->rollback();
    $conn->autocommit(true);
    
    // Error response
    $response = [
        'success' => false,
        'message' => $e->getMessage()
    ];
    
    echo json_encode($response);
    error_log("Error in simpan_mapping.php: " . $e->getMessage());
}
?>
